export const allowedImageFileTypes = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/jpg'
]
export const allowedPdfFileTypes = ['application/pdf']

export const collections = Object.freeze({
  users: {
    slug: 'users' as const,
    roles: {
      superAdmin: 'super-admin',
      editor: 'editor',
      user: 'user'
    } as const
  },
  categories: {
    slug: 'categories' as const
  },
  pages: {
    slug: 'pages' as const
  },
  posts: {
    slug: 'posts' as const
  },
  media: {
    slug: 'media' as const,
    fileTypes: [...allowedImageFileTypes, ...allowedPdfFileTypes],
    maxFileSize: 5 * 1024 * 1024, // 5MB,
    maxFileSizeMb: '5MB',
    uploadDir: 'media'
  },
  shared: {
    minLength: 1,
    maxLength: 60,
    passwordMinLength: 8
  },
  blocks: {
    archiveBlock: {
      slug: 'archive' as const
    },
    banner: {
      slug: 'banner' as const
    },
    callToAction: {
      slug: 'cta' as const
    },
    code: {
      slug: 'code' as const
    },
    content: {
      slug: 'content' as const
    },
    form: {
      slug: 'formBlock' as const
    },
    mediaBlock: {
      slug: 'mediaBlock' as const
    }
  },
  header: {
    slug: 'header' as const
  },
  footer: {
    slug: 'footer' as const
  },
  teams: {
    slug: 'teams' as const
  },
  projects: {
    slug: 'projects' as const
  },
  keywords: {
    slug: 'keywords' as const
  },
  assets: {
    slug: 'assets' as const,
    uploadDir: 'assets'
  },
  screenshots: {
    slug: 'screenshots' as const
  },
  products: {
    slug: 'products' as const
  },
  subscriptions: {
    slug: 'subscriptions' as const
  }
})

export const AdminCollectionsGroup = {
  application: 'Application',
  marketing: 'Marketing'
}

export const payload = Object.freeze({
  authTokenId: 'payload-token',
  emailExistsError: 'A user with the given email is already registered.',
  credentialsError: 'The email or password provided is incorrect.',
  userLocked: 'This user is locked due to having too many failed login attempts.',
  notFound: 'Not Found',
  tokenExpired: 'Token is either invalid or has expired.'
})

export const multiAuth = Object.freeze({
  storageKeys: {
    accounts: 'serp-accounts',
    activeAccount: 'serp-active-account',
    accountTokens: 'serp-account-tokens'
  },
  cookieNames: {
    activeToken: 'payload-token',
    accountSession: 'serp-session'
  }
})

export const auth = Object.freeze({
  hasAccountCookie: 'has-account',
  errors: {
    currentUserError: {
      digest: 'currentUserError'
    },
    noEmail: {
      digest: 'noEmail'
    },
    noPassword: {
      digest: 'noPassword'
    },
    invalidEmail: {
      digest: 'invalidEmail'
    },
    invalidPassword: {
      digest: 'invalidPassword'
    },
    createUserError: {
      digest: 'createUserError'
    },
    userAlreadyExists: {
      digest: 'userAlreadyExists'
    },
    authFailed: {
      digest: 'authFailed'
    },
    signOutError: {
      digest: 'signOutError'
    },
    badCredentials: {
      digest: 'badCredentials'
    },
    userLocked: {
      digest: 'userLocked'
    },
    userNotFound: {
      digest: 'userNotFound'
    },
    noToken: {
      digest: 'noToken'
    },
    resetPasswordError: {
      digest: 'resetPasswordError'
    },
    tokenExpired: {
      digest: 'invalidResetToken'
    },
    emailSendError: {
      digest: 'emailSendError'
    },
    invalidState: {
      digest: 'invalidState'
    },
    invalidCode: {
      digest: 'invalidCode'
    },
    providerError: {
      digest: 'providerError'
    },
    socialAuthFailed: {
      digest: 'socialAuthFailed'
    },
    invalidProvider: {
      digest: 'invalidProvider'
    },
    updateUserError: {
      digest: 'updateUserError'
    },
    missmatchError: {
      digest: 'missmatchError'
    }
  },
  types: {
    public: 'public',
    authenticated: 'authenticated',
    anonEnabled: 'anon-enabled'
  }
})
