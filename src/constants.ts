export const allowedImageFileTypes = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/jpg'
]
export const allowedPdfFileTypes = ['application/pdf']

export const collections = Object.freeze({
  users: {
    slug: 'users' as const,
    roles: {
      superAdmin: 'super-admin',
      editor: 'editor',
      user: 'user'
    } as const
  },
  categories: {
    slug: 'categories' as const
  },
  pages: {
    slug: 'pages' as const
  },
  posts: {
    slug: 'posts' as const
  },
  media: {
    slug: 'media' as const,
    fileTypes: [...allowedImageFileTypes, ...allowedPdfFileTypes],
    maxFileSize: 5 * 1024 * 1024, // 5MB,
    maxFileSizeMb: '5MB',
    uploadDir: 'media'
  },
  shared: {
    minLength: 1,
    maxLength: 60,
    passwordMinLength: 8
  },
  blocks: {
    archiveBlock: {
      slug: 'archive' as const
    },
    banner: {
      slug: 'banner' as const
    },
    callToAction: {
      slug: 'cta' as const
    },
    code: {
      slug: 'code' as const
    },
    content: {
      slug: 'content' as const
    },
    form: {
      slug: 'formBlock' as const
    },
    mediaBlock: {
      slug: 'mediaBlock' as const
    }
  },
  header: {
    slug: 'header' as const
  },
  footer: {
    slug: 'footer' as const
  },
  teams: {
    slug: 'teams' as const
  },
  projects: {
    slug: 'projects' as const
  },
  keywords: {
    slug: 'keywords' as const
  },
  assets: {
    slug: 'assets' as const,
    uploadDir: 'assets'
  },
  screenshots: {
    slug: 'screenshots' as const
  },
  products: {
    slug: 'products' as const
  },
  subscriptions: {
    slug: 'subscriptions' as const
  }
})

export const AdminCollectionsGroup = {
  application: 'Application',
  marketing: 'Marketing'
}

export const payload = Object.freeze({
  authTokenId: 'payload-token',
  emailExistsError: 'A user with the given email is already registered.',
  credentialsError: 'The email or password provided is incorrect.',
  userLocked: 'This user is locked due to having too many failed login attempts.',
  notFound: 'Not Found',
  tokenExpired: 'Token is either invalid or has expired.'
})
