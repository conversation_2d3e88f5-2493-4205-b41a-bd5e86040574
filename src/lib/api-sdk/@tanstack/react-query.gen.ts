// This file is auto-generated by @hey-api/openapi-ts

import { type Options, getApiPages, postApiPages, deleteApiPagesById, getApiPagesById, patchApiPagesById, getApiPosts, postApiPosts, deleteApiPostsById, getApiPostsById, patchApiPostsById, getApiMedia, postApiMedia, deleteApiMediaById, getApiMediaById, patchApiMediaById, getApiCategories, postApiCategories, deleteApiCategoriesById, getApiCategoriesById, patchApiCategoriesById, getApiUsers, postApiUsers, deleteApiUsersById, getApiUsersById, patchApiUsersById, getApiTeams, postApiTeams, deleteApiTeamsById, getApiTeamsById, patchApiTeamsById, getApiProjects, postApiProjects, deleteApiProjectsById, getApiProjectsById, patchApiProjectsById, getApiKeywords, postApiKeywords, deleteApiKeywordsById, getApiKeywordsById, patchApiKeywordsById, getApiAssets, postApiAssets, deleteApiAssetsById, getApiAssetsById, patchApiAssetsById, getApiScreenshots, postApiScreenshots, deleteApiScreenshotsById, getApiScreenshotsById, patchApiScreenshotsById, getApiProducts, postApiProducts, deleteApiProductsById, getApiProductsById, patchApiProductsById, getApiSubscriptions, postApiSubscriptions, deleteApiSubscriptionsById, getApiSubscriptionsById, patchApiSubscriptionsById, getApiRedirects, postApiRedirects, deleteApiRedirectsById, getApiRedirectsById, patchApiRedirectsById, getApiForms, postApiForms, deleteApiFormsById, getApiFormsById, patchApiFormsById, getApiFormSubmissions, postApiFormSubmissions, deleteApiFormSubmissionsById, getApiFormSubmissionsById, patchApiFormSubmissionsById, getApiSearch, postApiSearch, deleteApiSearchById, getApiSearchById, patchApiSearchById, getApiPayloadJobs, postApiPayloadJobs, deleteApiPayloadJobsById, getApiPayloadJobsById, patchApiPayloadJobsById, getApiPayloadLockedDocuments, postApiPayloadLockedDocuments, deleteApiPayloadLockedDocumentsById, getApiPayloadLockedDocumentsById, patchApiPayloadLockedDocumentsById, getApiPayloadPreferences, postApiPayloadPreferences, deleteApiPayloadPreferencesById, getApiPayloadPreferencesById, patchApiPayloadPreferencesById, getApiPayloadMigrations, postApiPayloadMigrations, deleteApiPayloadMigrationsById, getApiPayloadMigrationsById, patchApiPayloadMigrationsById, getApiGlobalsHeader, postApiGlobalsHeader, getApiGlobalsFooter, postApiGlobalsFooter } from '../sdk.gen';
import { queryOptions, infiniteQueryOptions, type InfiniteData, type DefaultError, type UseMutationOptions } from '@tanstack/react-query';
import type { GetApiPagesData, GetApiPagesResponse, PostApiPagesData, PostApiPagesResponse, DeleteApiPagesByIdData, DeleteApiPagesByIdResponse, GetApiPagesByIdData, PatchApiPagesByIdData, PatchApiPagesByIdResponse, GetApiPostsData, GetApiPostsResponse, PostApiPostsData, PostApiPostsResponse, DeleteApiPostsByIdData, DeleteApiPostsByIdResponse, GetApiPostsByIdData, PatchApiPostsByIdData, PatchApiPostsByIdResponse, GetApiMediaData, GetApiMediaResponse, PostApiMediaData, PostApiMediaResponse, DeleteApiMediaByIdData, DeleteApiMediaByIdResponse, GetApiMediaByIdData, PatchApiMediaByIdData, PatchApiMediaByIdResponse, GetApiCategoriesData, GetApiCategoriesResponse, PostApiCategoriesData, PostApiCategoriesResponse, DeleteApiCategoriesByIdData, DeleteApiCategoriesByIdResponse, GetApiCategoriesByIdData, PatchApiCategoriesByIdData, PatchApiCategoriesByIdResponse, GetApiUsersData, GetApiUsersResponse, PostApiUsersData, PostApiUsersResponse, DeleteApiUsersByIdData, DeleteApiUsersByIdResponse, GetApiUsersByIdData, PatchApiUsersByIdData, PatchApiUsersByIdResponse, GetApiTeamsData, GetApiTeamsResponse, PostApiTeamsData, PostApiTeamsResponse, DeleteApiTeamsByIdData, DeleteApiTeamsByIdResponse, GetApiTeamsByIdData, PatchApiTeamsByIdData, PatchApiTeamsByIdResponse, GetApiProjectsData, GetApiProjectsResponse, PostApiProjectsData, PostApiProjectsResponse, DeleteApiProjectsByIdData, DeleteApiProjectsByIdResponse, GetApiProjectsByIdData, PatchApiProjectsByIdData, PatchApiProjectsByIdResponse, GetApiKeywordsData, GetApiKeywordsResponse, PostApiKeywordsData, PostApiKeywordsResponse, DeleteApiKeywordsByIdData, DeleteApiKeywordsByIdResponse, GetApiKeywordsByIdData, PatchApiKeywordsByIdData, PatchApiKeywordsByIdResponse, GetApiAssetsData, GetApiAssetsResponse, PostApiAssetsData, PostApiAssetsResponse, DeleteApiAssetsByIdData, DeleteApiAssetsByIdResponse, GetApiAssetsByIdData, PatchApiAssetsByIdData, PatchApiAssetsByIdResponse, GetApiScreenshotsData, GetApiScreenshotsResponse, PostApiScreenshotsData, PostApiScreenshotsResponse, DeleteApiScreenshotsByIdData, DeleteApiScreenshotsByIdResponse, GetApiScreenshotsByIdData, PatchApiScreenshotsByIdData, PatchApiScreenshotsByIdResponse, GetApiProductsData, GetApiProductsResponse, PostApiProductsData, PostApiProductsResponse, DeleteApiProductsByIdData, DeleteApiProductsByIdResponse, GetApiProductsByIdData, PatchApiProductsByIdData, PatchApiProductsByIdResponse, GetApiSubscriptionsData, GetApiSubscriptionsResponse, PostApiSubscriptionsData, PostApiSubscriptionsResponse, DeleteApiSubscriptionsByIdData, DeleteApiSubscriptionsByIdResponse, GetApiSubscriptionsByIdData, PatchApiSubscriptionsByIdData, PatchApiSubscriptionsByIdResponse, GetApiRedirectsData, GetApiRedirectsResponse, PostApiRedirectsData, PostApiRedirectsResponse, DeleteApiRedirectsByIdData, DeleteApiRedirectsByIdResponse, GetApiRedirectsByIdData, PatchApiRedirectsByIdData, PatchApiRedirectsByIdResponse, GetApiFormsData, GetApiFormsResponse, PostApiFormsData, PostApiFormsResponse, DeleteApiFormsByIdData, DeleteApiFormsByIdResponse, GetApiFormsByIdData, PatchApiFormsByIdData, PatchApiFormsByIdResponse, GetApiFormSubmissionsData, GetApiFormSubmissionsResponse, PostApiFormSubmissionsData, PostApiFormSubmissionsResponse, DeleteApiFormSubmissionsByIdData, DeleteApiFormSubmissionsByIdResponse, GetApiFormSubmissionsByIdData, PatchApiFormSubmissionsByIdData, PatchApiFormSubmissionsByIdResponse, GetApiSearchData, GetApiSearchResponse, PostApiSearchData, PostApiSearchResponse, DeleteApiSearchByIdData, DeleteApiSearchByIdResponse, GetApiSearchByIdData, PatchApiSearchByIdData, PatchApiSearchByIdResponse, GetApiPayloadJobsData, GetApiPayloadJobsResponse, PostApiPayloadJobsData, PostApiPayloadJobsResponse, DeleteApiPayloadJobsByIdData, DeleteApiPayloadJobsByIdResponse, GetApiPayloadJobsByIdData, PatchApiPayloadJobsByIdData, PatchApiPayloadJobsByIdResponse, GetApiPayloadLockedDocumentsData, GetApiPayloadLockedDocumentsResponse, PostApiPayloadLockedDocumentsData, PostApiPayloadLockedDocumentsResponse, DeleteApiPayloadLockedDocumentsByIdData, DeleteApiPayloadLockedDocumentsByIdResponse, GetApiPayloadLockedDocumentsByIdData, PatchApiPayloadLockedDocumentsByIdData, PatchApiPayloadLockedDocumentsByIdResponse, GetApiPayloadPreferencesData, GetApiPayloadPreferencesResponse, PostApiPayloadPreferencesData, PostApiPayloadPreferencesResponse, DeleteApiPayloadPreferencesByIdData, DeleteApiPayloadPreferencesByIdResponse, GetApiPayloadPreferencesByIdData, PatchApiPayloadPreferencesByIdData, PatchApiPayloadPreferencesByIdResponse, GetApiPayloadMigrationsData, GetApiPayloadMigrationsResponse, PostApiPayloadMigrationsData, PostApiPayloadMigrationsResponse, DeleteApiPayloadMigrationsByIdData, DeleteApiPayloadMigrationsByIdResponse, GetApiPayloadMigrationsByIdData, PatchApiPayloadMigrationsByIdData, PatchApiPayloadMigrationsByIdResponse, GetApiGlobalsHeaderData, PostApiGlobalsHeaderData, PostApiGlobalsHeaderResponse, GetApiGlobalsFooterData, PostApiGlobalsFooterData, PostApiGlobalsFooterResponse } from '../types.gen';
import { client as _heyApiClient } from '../client.gen';

export type QueryKey<TOptions extends Options> = [
    Pick<TOptions, 'baseUrl' | 'body' | 'headers' | 'path' | 'query'> & {
        _id: string;
        _infinite?: boolean;
    }
];

const createQueryKey = <TOptions extends Options>(id: string, options?: TOptions, infinite?: boolean): [
    QueryKey<TOptions>[0]
] => {
    const params: QueryKey<TOptions>[0] = { _id: id, baseUrl: (options?.client ?? _heyApiClient).getConfig().baseUrl } as QueryKey<TOptions>[0];
    if (infinite) {
        params._infinite = infinite;
    }
    if (options?.body) {
        params.body = options.body;
    }
    if (options?.headers) {
        params.headers = options.headers;
    }
    if (options?.path) {
        params.path = options.path;
    }
    if (options?.query) {
        params.query = options.query;
    }
    return [
        params
    ];
};

export const getApiPagesQueryKey = (options?: Options<GetApiPagesData>) => createQueryKey('getApiPages', options);

/**
 * Retrieve a list of Pages
 */
export const getApiPagesOptions = (options?: Options<GetApiPagesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPages({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPagesQueryKey(options)
    });
};

const createInfiniteParams = <K extends Pick<QueryKey<Options>[0], 'body' | 'headers' | 'path' | 'query'>>(queryKey: QueryKey<Options>, page: K) => {
    const params = {
        ...queryKey[0]
    };
    if (page.body) {
        params.body = {
            ...queryKey[0].body as any,
            ...page.body as any
        };
    }
    if (page.headers) {
        params.headers = {
            ...queryKey[0].headers,
            ...page.headers
        };
    }
    if (page.path) {
        params.path = {
            ...queryKey[0].path as any,
            ...page.path as any
        };
    }
    if (page.query) {
        params.query = {
            ...queryKey[0].query as any,
            ...page.query as any
        };
    }
    return params as unknown as typeof page;
};

export const getApiPagesInfiniteQueryKey = (options?: Options<GetApiPagesData>): QueryKey<Options<GetApiPagesData>> => createQueryKey('getApiPages', options, true);

/**
 * Retrieve a list of Pages
 */
export const getApiPagesInfiniteOptions = (options?: Options<GetApiPagesData>) => {
    return infiniteQueryOptions<GetApiPagesResponse, DefaultError, InfiniteData<GetApiPagesResponse>, QueryKey<Options<GetApiPagesData>>, number | Pick<QueryKey<Options<GetApiPagesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiPagesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiPages({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPagesInfiniteQueryKey(options)
    });
};

export const postApiPagesQueryKey = (options?: Options<PostApiPagesData>) => createQueryKey('postApiPages', options);

/**
 * Create a new Page
 */
export const postApiPagesOptions = (options?: Options<PostApiPagesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiPages({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiPagesQueryKey(options)
    });
};

/**
 * Create a new Page
 */
export const postApiPagesMutation = (options?: Partial<Options<PostApiPagesData>>): UseMutationOptions<PostApiPagesResponse, DefaultError, Options<PostApiPagesData>> => {
    const mutationOptions: UseMutationOptions<PostApiPagesResponse, DefaultError, Options<PostApiPagesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiPages({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Page
 */
export const deleteApiPagesByIdMutation = (options?: Partial<Options<DeleteApiPagesByIdData>>): UseMutationOptions<DeleteApiPagesByIdResponse, DefaultError, Options<DeleteApiPagesByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiPagesByIdResponse, DefaultError, Options<DeleteApiPagesByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiPagesById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPagesByIdQueryKey = (options: Options<GetApiPagesByIdData>) => createQueryKey('getApiPagesById', options);

/**
 * Find a Page by ID
 */
export const getApiPagesByIdOptions = (options: Options<GetApiPagesByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPagesById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPagesByIdQueryKey(options)
    });
};

/**
 * Update a Page
 */
export const patchApiPagesByIdMutation = (options?: Partial<Options<PatchApiPagesByIdData>>): UseMutationOptions<PatchApiPagesByIdResponse, DefaultError, Options<PatchApiPagesByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiPagesByIdResponse, DefaultError, Options<PatchApiPagesByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiPagesById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPostsQueryKey = (options?: Options<GetApiPostsData>) => createQueryKey('getApiPosts', options);

/**
 * Retrieve a list of Posts
 */
export const getApiPostsOptions = (options?: Options<GetApiPostsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPosts({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPostsQueryKey(options)
    });
};

export const getApiPostsInfiniteQueryKey = (options?: Options<GetApiPostsData>): QueryKey<Options<GetApiPostsData>> => createQueryKey('getApiPosts', options, true);

/**
 * Retrieve a list of Posts
 */
export const getApiPostsInfiniteOptions = (options?: Options<GetApiPostsData>) => {
    return infiniteQueryOptions<GetApiPostsResponse, DefaultError, InfiniteData<GetApiPostsResponse>, QueryKey<Options<GetApiPostsData>>, number | Pick<QueryKey<Options<GetApiPostsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiPostsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiPosts({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPostsInfiniteQueryKey(options)
    });
};

export const postApiPostsQueryKey = (options?: Options<PostApiPostsData>) => createQueryKey('postApiPosts', options);

/**
 * Create a new Post
 */
export const postApiPostsOptions = (options?: Options<PostApiPostsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiPosts({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiPostsQueryKey(options)
    });
};

/**
 * Create a new Post
 */
export const postApiPostsMutation = (options?: Partial<Options<PostApiPostsData>>): UseMutationOptions<PostApiPostsResponse, DefaultError, Options<PostApiPostsData>> => {
    const mutationOptions: UseMutationOptions<PostApiPostsResponse, DefaultError, Options<PostApiPostsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiPosts({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Post
 */
export const deleteApiPostsByIdMutation = (options?: Partial<Options<DeleteApiPostsByIdData>>): UseMutationOptions<DeleteApiPostsByIdResponse, DefaultError, Options<DeleteApiPostsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiPostsByIdResponse, DefaultError, Options<DeleteApiPostsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiPostsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPostsByIdQueryKey = (options: Options<GetApiPostsByIdData>) => createQueryKey('getApiPostsById', options);

/**
 * Find a Post by ID
 */
export const getApiPostsByIdOptions = (options: Options<GetApiPostsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPostsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPostsByIdQueryKey(options)
    });
};

/**
 * Update a Post
 */
export const patchApiPostsByIdMutation = (options?: Partial<Options<PatchApiPostsByIdData>>): UseMutationOptions<PatchApiPostsByIdResponse, DefaultError, Options<PatchApiPostsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiPostsByIdResponse, DefaultError, Options<PatchApiPostsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiPostsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiMediaQueryKey = (options?: Options<GetApiMediaData>) => createQueryKey('getApiMedia', options);

/**
 * Retrieve a list of Media
 */
export const getApiMediaOptions = (options?: Options<GetApiMediaData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiMedia({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiMediaQueryKey(options)
    });
};

export const getApiMediaInfiniteQueryKey = (options?: Options<GetApiMediaData>): QueryKey<Options<GetApiMediaData>> => createQueryKey('getApiMedia', options, true);

/**
 * Retrieve a list of Media
 */
export const getApiMediaInfiniteOptions = (options?: Options<GetApiMediaData>) => {
    return infiniteQueryOptions<GetApiMediaResponse, DefaultError, InfiniteData<GetApiMediaResponse>, QueryKey<Options<GetApiMediaData>>, number | Pick<QueryKey<Options<GetApiMediaData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiMediaData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiMedia({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiMediaInfiniteQueryKey(options)
    });
};

export const postApiMediaQueryKey = (options?: Options<PostApiMediaData>) => createQueryKey('postApiMedia', options);

/**
 * Create a new Media
 */
export const postApiMediaOptions = (options?: Options<PostApiMediaData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiMedia({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiMediaQueryKey(options)
    });
};

/**
 * Create a new Media
 */
export const postApiMediaMutation = (options?: Partial<Options<PostApiMediaData>>): UseMutationOptions<PostApiMediaResponse, DefaultError, Options<PostApiMediaData>> => {
    const mutationOptions: UseMutationOptions<PostApiMediaResponse, DefaultError, Options<PostApiMediaData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiMedia({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Media
 */
export const deleteApiMediaByIdMutation = (options?: Partial<Options<DeleteApiMediaByIdData>>): UseMutationOptions<DeleteApiMediaByIdResponse, DefaultError, Options<DeleteApiMediaByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiMediaByIdResponse, DefaultError, Options<DeleteApiMediaByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiMediaById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiMediaByIdQueryKey = (options: Options<GetApiMediaByIdData>) => createQueryKey('getApiMediaById', options);

/**
 * Find a Media by ID
 */
export const getApiMediaByIdOptions = (options: Options<GetApiMediaByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiMediaById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiMediaByIdQueryKey(options)
    });
};

/**
 * Update a Media
 */
export const patchApiMediaByIdMutation = (options?: Partial<Options<PatchApiMediaByIdData>>): UseMutationOptions<PatchApiMediaByIdResponse, DefaultError, Options<PatchApiMediaByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiMediaByIdResponse, DefaultError, Options<PatchApiMediaByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiMediaById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiCategoriesQueryKey = (options?: Options<GetApiCategoriesData>) => createQueryKey('getApiCategories', options);

/**
 * Retrieve a list of Categories
 */
export const getApiCategoriesOptions = (options?: Options<GetApiCategoriesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiCategories({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiCategoriesQueryKey(options)
    });
};

export const getApiCategoriesInfiniteQueryKey = (options?: Options<GetApiCategoriesData>): QueryKey<Options<GetApiCategoriesData>> => createQueryKey('getApiCategories', options, true);

/**
 * Retrieve a list of Categories
 */
export const getApiCategoriesInfiniteOptions = (options?: Options<GetApiCategoriesData>) => {
    return infiniteQueryOptions<GetApiCategoriesResponse, DefaultError, InfiniteData<GetApiCategoriesResponse>, QueryKey<Options<GetApiCategoriesData>>, number | Pick<QueryKey<Options<GetApiCategoriesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiCategoriesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiCategories({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiCategoriesInfiniteQueryKey(options)
    });
};

export const postApiCategoriesQueryKey = (options?: Options<PostApiCategoriesData>) => createQueryKey('postApiCategories', options);

/**
 * Create a new Category
 */
export const postApiCategoriesOptions = (options?: Options<PostApiCategoriesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiCategories({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiCategoriesQueryKey(options)
    });
};

/**
 * Create a new Category
 */
export const postApiCategoriesMutation = (options?: Partial<Options<PostApiCategoriesData>>): UseMutationOptions<PostApiCategoriesResponse, DefaultError, Options<PostApiCategoriesData>> => {
    const mutationOptions: UseMutationOptions<PostApiCategoriesResponse, DefaultError, Options<PostApiCategoriesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiCategories({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Category
 */
export const deleteApiCategoriesByIdMutation = (options?: Partial<Options<DeleteApiCategoriesByIdData>>): UseMutationOptions<DeleteApiCategoriesByIdResponse, DefaultError, Options<DeleteApiCategoriesByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiCategoriesByIdResponse, DefaultError, Options<DeleteApiCategoriesByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiCategoriesById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiCategoriesByIdQueryKey = (options: Options<GetApiCategoriesByIdData>) => createQueryKey('getApiCategoriesById', options);

/**
 * Find a Category by ID
 */
export const getApiCategoriesByIdOptions = (options: Options<GetApiCategoriesByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiCategoriesById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiCategoriesByIdQueryKey(options)
    });
};

/**
 * Update a Category
 */
export const patchApiCategoriesByIdMutation = (options?: Partial<Options<PatchApiCategoriesByIdData>>): UseMutationOptions<PatchApiCategoriesByIdResponse, DefaultError, Options<PatchApiCategoriesByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiCategoriesByIdResponse, DefaultError, Options<PatchApiCategoriesByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiCategoriesById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiUsersQueryKey = (options?: Options<GetApiUsersData>) => createQueryKey('getApiUsers', options);

/**
 * Retrieve a list of Users
 */
export const getApiUsersOptions = (options?: Options<GetApiUsersData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiUsers({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiUsersQueryKey(options)
    });
};

export const getApiUsersInfiniteQueryKey = (options?: Options<GetApiUsersData>): QueryKey<Options<GetApiUsersData>> => createQueryKey('getApiUsers', options, true);

/**
 * Retrieve a list of Users
 */
export const getApiUsersInfiniteOptions = (options?: Options<GetApiUsersData>) => {
    return infiniteQueryOptions<GetApiUsersResponse, DefaultError, InfiniteData<GetApiUsersResponse>, QueryKey<Options<GetApiUsersData>>, number | Pick<QueryKey<Options<GetApiUsersData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiUsersData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiUsers({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiUsersInfiniteQueryKey(options)
    });
};

export const postApiUsersQueryKey = (options?: Options<PostApiUsersData>) => createQueryKey('postApiUsers', options);

/**
 * Create a new User
 */
export const postApiUsersOptions = (options?: Options<PostApiUsersData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiUsers({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiUsersQueryKey(options)
    });
};

/**
 * Create a new User
 */
export const postApiUsersMutation = (options?: Partial<Options<PostApiUsersData>>): UseMutationOptions<PostApiUsersResponse, DefaultError, Options<PostApiUsersData>> => {
    const mutationOptions: UseMutationOptions<PostApiUsersResponse, DefaultError, Options<PostApiUsersData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiUsers({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a User
 */
export const deleteApiUsersByIdMutation = (options?: Partial<Options<DeleteApiUsersByIdData>>): UseMutationOptions<DeleteApiUsersByIdResponse, DefaultError, Options<DeleteApiUsersByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiUsersByIdResponse, DefaultError, Options<DeleteApiUsersByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiUsersById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiUsersByIdQueryKey = (options: Options<GetApiUsersByIdData>) => createQueryKey('getApiUsersById', options);

/**
 * Find a User by ID
 */
export const getApiUsersByIdOptions = (options: Options<GetApiUsersByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiUsersById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiUsersByIdQueryKey(options)
    });
};

/**
 * Update a User
 */
export const patchApiUsersByIdMutation = (options?: Partial<Options<PatchApiUsersByIdData>>): UseMutationOptions<PatchApiUsersByIdResponse, DefaultError, Options<PatchApiUsersByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiUsersByIdResponse, DefaultError, Options<PatchApiUsersByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiUsersById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiTeamsQueryKey = (options?: Options<GetApiTeamsData>) => createQueryKey('getApiTeams', options);

/**
 * Retrieve a list of Teams
 */
export const getApiTeamsOptions = (options?: Options<GetApiTeamsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiTeams({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiTeamsQueryKey(options)
    });
};

export const getApiTeamsInfiniteQueryKey = (options?: Options<GetApiTeamsData>): QueryKey<Options<GetApiTeamsData>> => createQueryKey('getApiTeams', options, true);

/**
 * Retrieve a list of Teams
 */
export const getApiTeamsInfiniteOptions = (options?: Options<GetApiTeamsData>) => {
    return infiniteQueryOptions<GetApiTeamsResponse, DefaultError, InfiniteData<GetApiTeamsResponse>, QueryKey<Options<GetApiTeamsData>>, number | Pick<QueryKey<Options<GetApiTeamsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiTeamsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiTeams({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiTeamsInfiniteQueryKey(options)
    });
};

export const postApiTeamsQueryKey = (options?: Options<PostApiTeamsData>) => createQueryKey('postApiTeams', options);

/**
 * Create a new Team
 */
export const postApiTeamsOptions = (options?: Options<PostApiTeamsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiTeams({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiTeamsQueryKey(options)
    });
};

/**
 * Create a new Team
 */
export const postApiTeamsMutation = (options?: Partial<Options<PostApiTeamsData>>): UseMutationOptions<PostApiTeamsResponse, DefaultError, Options<PostApiTeamsData>> => {
    const mutationOptions: UseMutationOptions<PostApiTeamsResponse, DefaultError, Options<PostApiTeamsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiTeams({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Team
 */
export const deleteApiTeamsByIdMutation = (options?: Partial<Options<DeleteApiTeamsByIdData>>): UseMutationOptions<DeleteApiTeamsByIdResponse, DefaultError, Options<DeleteApiTeamsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiTeamsByIdResponse, DefaultError, Options<DeleteApiTeamsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiTeamsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiTeamsByIdQueryKey = (options: Options<GetApiTeamsByIdData>) => createQueryKey('getApiTeamsById', options);

/**
 * Find a Team by ID
 */
export const getApiTeamsByIdOptions = (options: Options<GetApiTeamsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiTeamsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiTeamsByIdQueryKey(options)
    });
};

/**
 * Update a Team
 */
export const patchApiTeamsByIdMutation = (options?: Partial<Options<PatchApiTeamsByIdData>>): UseMutationOptions<PatchApiTeamsByIdResponse, DefaultError, Options<PatchApiTeamsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiTeamsByIdResponse, DefaultError, Options<PatchApiTeamsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiTeamsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiProjectsQueryKey = (options?: Options<GetApiProjectsData>) => createQueryKey('getApiProjects', options);

/**
 * Retrieve a list of Projects
 */
export const getApiProjectsOptions = (options?: Options<GetApiProjectsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiProjects({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiProjectsQueryKey(options)
    });
};

export const getApiProjectsInfiniteQueryKey = (options?: Options<GetApiProjectsData>): QueryKey<Options<GetApiProjectsData>> => createQueryKey('getApiProjects', options, true);

/**
 * Retrieve a list of Projects
 */
export const getApiProjectsInfiniteOptions = (options?: Options<GetApiProjectsData>) => {
    return infiniteQueryOptions<GetApiProjectsResponse, DefaultError, InfiniteData<GetApiProjectsResponse>, QueryKey<Options<GetApiProjectsData>>, number | Pick<QueryKey<Options<GetApiProjectsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiProjectsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiProjects({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiProjectsInfiniteQueryKey(options)
    });
};

export const postApiProjectsQueryKey = (options?: Options<PostApiProjectsData>) => createQueryKey('postApiProjects', options);

/**
 * Create a new Project
 */
export const postApiProjectsOptions = (options?: Options<PostApiProjectsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiProjects({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiProjectsQueryKey(options)
    });
};

/**
 * Create a new Project
 */
export const postApiProjectsMutation = (options?: Partial<Options<PostApiProjectsData>>): UseMutationOptions<PostApiProjectsResponse, DefaultError, Options<PostApiProjectsData>> => {
    const mutationOptions: UseMutationOptions<PostApiProjectsResponse, DefaultError, Options<PostApiProjectsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiProjects({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Project
 */
export const deleteApiProjectsByIdMutation = (options?: Partial<Options<DeleteApiProjectsByIdData>>): UseMutationOptions<DeleteApiProjectsByIdResponse, DefaultError, Options<DeleteApiProjectsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiProjectsByIdResponse, DefaultError, Options<DeleteApiProjectsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiProjectsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiProjectsByIdQueryKey = (options: Options<GetApiProjectsByIdData>) => createQueryKey('getApiProjectsById', options);

/**
 * Find a Project by ID
 */
export const getApiProjectsByIdOptions = (options: Options<GetApiProjectsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiProjectsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiProjectsByIdQueryKey(options)
    });
};

/**
 * Update a Project
 */
export const patchApiProjectsByIdMutation = (options?: Partial<Options<PatchApiProjectsByIdData>>): UseMutationOptions<PatchApiProjectsByIdResponse, DefaultError, Options<PatchApiProjectsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiProjectsByIdResponse, DefaultError, Options<PatchApiProjectsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiProjectsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiKeywordsQueryKey = (options?: Options<GetApiKeywordsData>) => createQueryKey('getApiKeywords', options);

/**
 * Retrieve a list of Keywords
 */
export const getApiKeywordsOptions = (options?: Options<GetApiKeywordsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiKeywords({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiKeywordsQueryKey(options)
    });
};

export const getApiKeywordsInfiniteQueryKey = (options?: Options<GetApiKeywordsData>): QueryKey<Options<GetApiKeywordsData>> => createQueryKey('getApiKeywords', options, true);

/**
 * Retrieve a list of Keywords
 */
export const getApiKeywordsInfiniteOptions = (options?: Options<GetApiKeywordsData>) => {
    return infiniteQueryOptions<GetApiKeywordsResponse, DefaultError, InfiniteData<GetApiKeywordsResponse>, QueryKey<Options<GetApiKeywordsData>>, number | Pick<QueryKey<Options<GetApiKeywordsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiKeywordsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiKeywords({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiKeywordsInfiniteQueryKey(options)
    });
};

export const postApiKeywordsQueryKey = (options?: Options<PostApiKeywordsData>) => createQueryKey('postApiKeywords', options);

/**
 * Create a new Keyword
 */
export const postApiKeywordsOptions = (options?: Options<PostApiKeywordsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiKeywords({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiKeywordsQueryKey(options)
    });
};

/**
 * Create a new Keyword
 */
export const postApiKeywordsMutation = (options?: Partial<Options<PostApiKeywordsData>>): UseMutationOptions<PostApiKeywordsResponse, DefaultError, Options<PostApiKeywordsData>> => {
    const mutationOptions: UseMutationOptions<PostApiKeywordsResponse, DefaultError, Options<PostApiKeywordsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiKeywords({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Keyword
 */
export const deleteApiKeywordsByIdMutation = (options?: Partial<Options<DeleteApiKeywordsByIdData>>): UseMutationOptions<DeleteApiKeywordsByIdResponse, DefaultError, Options<DeleteApiKeywordsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiKeywordsByIdResponse, DefaultError, Options<DeleteApiKeywordsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiKeywordsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiKeywordsByIdQueryKey = (options: Options<GetApiKeywordsByIdData>) => createQueryKey('getApiKeywordsById', options);

/**
 * Find a Keyword by ID
 */
export const getApiKeywordsByIdOptions = (options: Options<GetApiKeywordsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiKeywordsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiKeywordsByIdQueryKey(options)
    });
};

/**
 * Update a Keyword
 */
export const patchApiKeywordsByIdMutation = (options?: Partial<Options<PatchApiKeywordsByIdData>>): UseMutationOptions<PatchApiKeywordsByIdResponse, DefaultError, Options<PatchApiKeywordsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiKeywordsByIdResponse, DefaultError, Options<PatchApiKeywordsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiKeywordsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiAssetsQueryKey = (options?: Options<GetApiAssetsData>) => createQueryKey('getApiAssets', options);

/**
 * Retrieve a list of Assets
 */
export const getApiAssetsOptions = (options?: Options<GetApiAssetsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiAssets({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiAssetsQueryKey(options)
    });
};

export const getApiAssetsInfiniteQueryKey = (options?: Options<GetApiAssetsData>): QueryKey<Options<GetApiAssetsData>> => createQueryKey('getApiAssets', options, true);

/**
 * Retrieve a list of Assets
 */
export const getApiAssetsInfiniteOptions = (options?: Options<GetApiAssetsData>) => {
    return infiniteQueryOptions<GetApiAssetsResponse, DefaultError, InfiniteData<GetApiAssetsResponse>, QueryKey<Options<GetApiAssetsData>>, number | Pick<QueryKey<Options<GetApiAssetsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiAssetsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiAssets({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiAssetsInfiniteQueryKey(options)
    });
};

export const postApiAssetsQueryKey = (options?: Options<PostApiAssetsData>) => createQueryKey('postApiAssets', options);

/**
 * Create a new Asset
 */
export const postApiAssetsOptions = (options?: Options<PostApiAssetsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiAssets({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiAssetsQueryKey(options)
    });
};

/**
 * Create a new Asset
 */
export const postApiAssetsMutation = (options?: Partial<Options<PostApiAssetsData>>): UseMutationOptions<PostApiAssetsResponse, DefaultError, Options<PostApiAssetsData>> => {
    const mutationOptions: UseMutationOptions<PostApiAssetsResponse, DefaultError, Options<PostApiAssetsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiAssets({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Asset
 */
export const deleteApiAssetsByIdMutation = (options?: Partial<Options<DeleteApiAssetsByIdData>>): UseMutationOptions<DeleteApiAssetsByIdResponse, DefaultError, Options<DeleteApiAssetsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiAssetsByIdResponse, DefaultError, Options<DeleteApiAssetsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiAssetsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiAssetsByIdQueryKey = (options: Options<GetApiAssetsByIdData>) => createQueryKey('getApiAssetsById', options);

/**
 * Find a Asset by ID
 */
export const getApiAssetsByIdOptions = (options: Options<GetApiAssetsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiAssetsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiAssetsByIdQueryKey(options)
    });
};

/**
 * Update a Asset
 */
export const patchApiAssetsByIdMutation = (options?: Partial<Options<PatchApiAssetsByIdData>>): UseMutationOptions<PatchApiAssetsByIdResponse, DefaultError, Options<PatchApiAssetsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiAssetsByIdResponse, DefaultError, Options<PatchApiAssetsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiAssetsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiScreenshotsQueryKey = (options?: Options<GetApiScreenshotsData>) => createQueryKey('getApiScreenshots', options);

/**
 * Retrieve a list of Screenshots
 */
export const getApiScreenshotsOptions = (options?: Options<GetApiScreenshotsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiScreenshots({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiScreenshotsQueryKey(options)
    });
};

export const getApiScreenshotsInfiniteQueryKey = (options?: Options<GetApiScreenshotsData>): QueryKey<Options<GetApiScreenshotsData>> => createQueryKey('getApiScreenshots', options, true);

/**
 * Retrieve a list of Screenshots
 */
export const getApiScreenshotsInfiniteOptions = (options?: Options<GetApiScreenshotsData>) => {
    return infiniteQueryOptions<GetApiScreenshotsResponse, DefaultError, InfiniteData<GetApiScreenshotsResponse>, QueryKey<Options<GetApiScreenshotsData>>, number | Pick<QueryKey<Options<GetApiScreenshotsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiScreenshotsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiScreenshots({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiScreenshotsInfiniteQueryKey(options)
    });
};

export const postApiScreenshotsQueryKey = (options?: Options<PostApiScreenshotsData>) => createQueryKey('postApiScreenshots', options);

/**
 * Create a new Screenshot
 */
export const postApiScreenshotsOptions = (options?: Options<PostApiScreenshotsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiScreenshots({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiScreenshotsQueryKey(options)
    });
};

/**
 * Create a new Screenshot
 */
export const postApiScreenshotsMutation = (options?: Partial<Options<PostApiScreenshotsData>>): UseMutationOptions<PostApiScreenshotsResponse, DefaultError, Options<PostApiScreenshotsData>> => {
    const mutationOptions: UseMutationOptions<PostApiScreenshotsResponse, DefaultError, Options<PostApiScreenshotsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiScreenshots({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Screenshot
 */
export const deleteApiScreenshotsByIdMutation = (options?: Partial<Options<DeleteApiScreenshotsByIdData>>): UseMutationOptions<DeleteApiScreenshotsByIdResponse, DefaultError, Options<DeleteApiScreenshotsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiScreenshotsByIdResponse, DefaultError, Options<DeleteApiScreenshotsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiScreenshotsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiScreenshotsByIdQueryKey = (options: Options<GetApiScreenshotsByIdData>) => createQueryKey('getApiScreenshotsById', options);

/**
 * Find a Screenshot by ID
 */
export const getApiScreenshotsByIdOptions = (options: Options<GetApiScreenshotsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiScreenshotsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiScreenshotsByIdQueryKey(options)
    });
};

/**
 * Update a Screenshot
 */
export const patchApiScreenshotsByIdMutation = (options?: Partial<Options<PatchApiScreenshotsByIdData>>): UseMutationOptions<PatchApiScreenshotsByIdResponse, DefaultError, Options<PatchApiScreenshotsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiScreenshotsByIdResponse, DefaultError, Options<PatchApiScreenshotsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiScreenshotsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiProductsQueryKey = (options?: Options<GetApiProductsData>) => createQueryKey('getApiProducts', options);

/**
 * Retrieve a list of Products
 */
export const getApiProductsOptions = (options?: Options<GetApiProductsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiProducts({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiProductsQueryKey(options)
    });
};

export const getApiProductsInfiniteQueryKey = (options?: Options<GetApiProductsData>): QueryKey<Options<GetApiProductsData>> => createQueryKey('getApiProducts', options, true);

/**
 * Retrieve a list of Products
 */
export const getApiProductsInfiniteOptions = (options?: Options<GetApiProductsData>) => {
    return infiniteQueryOptions<GetApiProductsResponse, DefaultError, InfiniteData<GetApiProductsResponse>, QueryKey<Options<GetApiProductsData>>, number | Pick<QueryKey<Options<GetApiProductsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiProductsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiProducts({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiProductsInfiniteQueryKey(options)
    });
};

export const postApiProductsQueryKey = (options?: Options<PostApiProductsData>) => createQueryKey('postApiProducts', options);

/**
 * Create a new Product
 */
export const postApiProductsOptions = (options?: Options<PostApiProductsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiProducts({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiProductsQueryKey(options)
    });
};

/**
 * Create a new Product
 */
export const postApiProductsMutation = (options?: Partial<Options<PostApiProductsData>>): UseMutationOptions<PostApiProductsResponse, DefaultError, Options<PostApiProductsData>> => {
    const mutationOptions: UseMutationOptions<PostApiProductsResponse, DefaultError, Options<PostApiProductsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiProducts({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Product
 */
export const deleteApiProductsByIdMutation = (options?: Partial<Options<DeleteApiProductsByIdData>>): UseMutationOptions<DeleteApiProductsByIdResponse, DefaultError, Options<DeleteApiProductsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiProductsByIdResponse, DefaultError, Options<DeleteApiProductsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiProductsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiProductsByIdQueryKey = (options: Options<GetApiProductsByIdData>) => createQueryKey('getApiProductsById', options);

/**
 * Find a Product by ID
 */
export const getApiProductsByIdOptions = (options: Options<GetApiProductsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiProductsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiProductsByIdQueryKey(options)
    });
};

/**
 * Update a Product
 */
export const patchApiProductsByIdMutation = (options?: Partial<Options<PatchApiProductsByIdData>>): UseMutationOptions<PatchApiProductsByIdResponse, DefaultError, Options<PatchApiProductsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiProductsByIdResponse, DefaultError, Options<PatchApiProductsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiProductsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiSubscriptionsQueryKey = (options?: Options<GetApiSubscriptionsData>) => createQueryKey('getApiSubscriptions', options);

/**
 * Retrieve a list of Subscriptions
 */
export const getApiSubscriptionsOptions = (options?: Options<GetApiSubscriptionsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiSubscriptions({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiSubscriptionsQueryKey(options)
    });
};

export const getApiSubscriptionsInfiniteQueryKey = (options?: Options<GetApiSubscriptionsData>): QueryKey<Options<GetApiSubscriptionsData>> => createQueryKey('getApiSubscriptions', options, true);

/**
 * Retrieve a list of Subscriptions
 */
export const getApiSubscriptionsInfiniteOptions = (options?: Options<GetApiSubscriptionsData>) => {
    return infiniteQueryOptions<GetApiSubscriptionsResponse, DefaultError, InfiniteData<GetApiSubscriptionsResponse>, QueryKey<Options<GetApiSubscriptionsData>>, number | Pick<QueryKey<Options<GetApiSubscriptionsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiSubscriptionsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiSubscriptions({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiSubscriptionsInfiniteQueryKey(options)
    });
};

export const postApiSubscriptionsQueryKey = (options?: Options<PostApiSubscriptionsData>) => createQueryKey('postApiSubscriptions', options);

/**
 * Create a new Subscription
 */
export const postApiSubscriptionsOptions = (options?: Options<PostApiSubscriptionsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiSubscriptions({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiSubscriptionsQueryKey(options)
    });
};

/**
 * Create a new Subscription
 */
export const postApiSubscriptionsMutation = (options?: Partial<Options<PostApiSubscriptionsData>>): UseMutationOptions<PostApiSubscriptionsResponse, DefaultError, Options<PostApiSubscriptionsData>> => {
    const mutationOptions: UseMutationOptions<PostApiSubscriptionsResponse, DefaultError, Options<PostApiSubscriptionsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiSubscriptions({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Subscription
 */
export const deleteApiSubscriptionsByIdMutation = (options?: Partial<Options<DeleteApiSubscriptionsByIdData>>): UseMutationOptions<DeleteApiSubscriptionsByIdResponse, DefaultError, Options<DeleteApiSubscriptionsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiSubscriptionsByIdResponse, DefaultError, Options<DeleteApiSubscriptionsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiSubscriptionsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiSubscriptionsByIdQueryKey = (options: Options<GetApiSubscriptionsByIdData>) => createQueryKey('getApiSubscriptionsById', options);

/**
 * Find a Subscription by ID
 */
export const getApiSubscriptionsByIdOptions = (options: Options<GetApiSubscriptionsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiSubscriptionsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiSubscriptionsByIdQueryKey(options)
    });
};

/**
 * Update a Subscription
 */
export const patchApiSubscriptionsByIdMutation = (options?: Partial<Options<PatchApiSubscriptionsByIdData>>): UseMutationOptions<PatchApiSubscriptionsByIdResponse, DefaultError, Options<PatchApiSubscriptionsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiSubscriptionsByIdResponse, DefaultError, Options<PatchApiSubscriptionsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiSubscriptionsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiRedirectsQueryKey = (options?: Options<GetApiRedirectsData>) => createQueryKey('getApiRedirects', options);

/**
 * Retrieve a list of Redirects
 */
export const getApiRedirectsOptions = (options?: Options<GetApiRedirectsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiRedirects({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRedirectsQueryKey(options)
    });
};

export const getApiRedirectsInfiniteQueryKey = (options?: Options<GetApiRedirectsData>): QueryKey<Options<GetApiRedirectsData>> => createQueryKey('getApiRedirects', options, true);

/**
 * Retrieve a list of Redirects
 */
export const getApiRedirectsInfiniteOptions = (options?: Options<GetApiRedirectsData>) => {
    return infiniteQueryOptions<GetApiRedirectsResponse, DefaultError, InfiniteData<GetApiRedirectsResponse>, QueryKey<Options<GetApiRedirectsData>>, number | Pick<QueryKey<Options<GetApiRedirectsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiRedirectsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiRedirects({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRedirectsInfiniteQueryKey(options)
    });
};

export const postApiRedirectsQueryKey = (options?: Options<PostApiRedirectsData>) => createQueryKey('postApiRedirects', options);

/**
 * Create a new Redirect
 */
export const postApiRedirectsOptions = (options?: Options<PostApiRedirectsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiRedirects({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiRedirectsQueryKey(options)
    });
};

/**
 * Create a new Redirect
 */
export const postApiRedirectsMutation = (options?: Partial<Options<PostApiRedirectsData>>): UseMutationOptions<PostApiRedirectsResponse, DefaultError, Options<PostApiRedirectsData>> => {
    const mutationOptions: UseMutationOptions<PostApiRedirectsResponse, DefaultError, Options<PostApiRedirectsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiRedirects({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Redirect
 */
export const deleteApiRedirectsByIdMutation = (options?: Partial<Options<DeleteApiRedirectsByIdData>>): UseMutationOptions<DeleteApiRedirectsByIdResponse, DefaultError, Options<DeleteApiRedirectsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiRedirectsByIdResponse, DefaultError, Options<DeleteApiRedirectsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiRedirectsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiRedirectsByIdQueryKey = (options: Options<GetApiRedirectsByIdData>) => createQueryKey('getApiRedirectsById', options);

/**
 * Find a Redirect by ID
 */
export const getApiRedirectsByIdOptions = (options: Options<GetApiRedirectsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiRedirectsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRedirectsByIdQueryKey(options)
    });
};

/**
 * Update a Redirect
 */
export const patchApiRedirectsByIdMutation = (options?: Partial<Options<PatchApiRedirectsByIdData>>): UseMutationOptions<PatchApiRedirectsByIdResponse, DefaultError, Options<PatchApiRedirectsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiRedirectsByIdResponse, DefaultError, Options<PatchApiRedirectsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiRedirectsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiFormsQueryKey = (options?: Options<GetApiFormsData>) => createQueryKey('getApiForms', options);

/**
 * Retrieve a list of Forms
 */
export const getApiFormsOptions = (options?: Options<GetApiFormsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiForms({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiFormsQueryKey(options)
    });
};

export const getApiFormsInfiniteQueryKey = (options?: Options<GetApiFormsData>): QueryKey<Options<GetApiFormsData>> => createQueryKey('getApiForms', options, true);

/**
 * Retrieve a list of Forms
 */
export const getApiFormsInfiniteOptions = (options?: Options<GetApiFormsData>) => {
    return infiniteQueryOptions<GetApiFormsResponse, DefaultError, InfiniteData<GetApiFormsResponse>, QueryKey<Options<GetApiFormsData>>, number | Pick<QueryKey<Options<GetApiFormsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiFormsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiForms({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiFormsInfiniteQueryKey(options)
    });
};

export const postApiFormsQueryKey = (options?: Options<PostApiFormsData>) => createQueryKey('postApiForms', options);

/**
 * Create a new Form
 */
export const postApiFormsOptions = (options?: Options<PostApiFormsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiForms({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiFormsQueryKey(options)
    });
};

/**
 * Create a new Form
 */
export const postApiFormsMutation = (options?: Partial<Options<PostApiFormsData>>): UseMutationOptions<PostApiFormsResponse, DefaultError, Options<PostApiFormsData>> => {
    const mutationOptions: UseMutationOptions<PostApiFormsResponse, DefaultError, Options<PostApiFormsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiForms({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Form
 */
export const deleteApiFormsByIdMutation = (options?: Partial<Options<DeleteApiFormsByIdData>>): UseMutationOptions<DeleteApiFormsByIdResponse, DefaultError, Options<DeleteApiFormsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiFormsByIdResponse, DefaultError, Options<DeleteApiFormsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiFormsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiFormsByIdQueryKey = (options: Options<GetApiFormsByIdData>) => createQueryKey('getApiFormsById', options);

/**
 * Find a Form by ID
 */
export const getApiFormsByIdOptions = (options: Options<GetApiFormsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiFormsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiFormsByIdQueryKey(options)
    });
};

/**
 * Update a Form
 */
export const patchApiFormsByIdMutation = (options?: Partial<Options<PatchApiFormsByIdData>>): UseMutationOptions<PatchApiFormsByIdResponse, DefaultError, Options<PatchApiFormsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiFormsByIdResponse, DefaultError, Options<PatchApiFormsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiFormsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiFormSubmissionsQueryKey = (options?: Options<GetApiFormSubmissionsData>) => createQueryKey('getApiFormSubmissions', options);

/**
 * Retrieve a list of Form Submissions
 */
export const getApiFormSubmissionsOptions = (options?: Options<GetApiFormSubmissionsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiFormSubmissions({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiFormSubmissionsQueryKey(options)
    });
};

export const getApiFormSubmissionsInfiniteQueryKey = (options?: Options<GetApiFormSubmissionsData>): QueryKey<Options<GetApiFormSubmissionsData>> => createQueryKey('getApiFormSubmissions', options, true);

/**
 * Retrieve a list of Form Submissions
 */
export const getApiFormSubmissionsInfiniteOptions = (options?: Options<GetApiFormSubmissionsData>) => {
    return infiniteQueryOptions<GetApiFormSubmissionsResponse, DefaultError, InfiniteData<GetApiFormSubmissionsResponse>, QueryKey<Options<GetApiFormSubmissionsData>>, number | Pick<QueryKey<Options<GetApiFormSubmissionsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiFormSubmissionsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiFormSubmissions({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiFormSubmissionsInfiniteQueryKey(options)
    });
};

export const postApiFormSubmissionsQueryKey = (options?: Options<PostApiFormSubmissionsData>) => createQueryKey('postApiFormSubmissions', options);

/**
 * Create a new Form Submission
 */
export const postApiFormSubmissionsOptions = (options?: Options<PostApiFormSubmissionsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiFormSubmissions({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiFormSubmissionsQueryKey(options)
    });
};

/**
 * Create a new Form Submission
 */
export const postApiFormSubmissionsMutation = (options?: Partial<Options<PostApiFormSubmissionsData>>): UseMutationOptions<PostApiFormSubmissionsResponse, DefaultError, Options<PostApiFormSubmissionsData>> => {
    const mutationOptions: UseMutationOptions<PostApiFormSubmissionsResponse, DefaultError, Options<PostApiFormSubmissionsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiFormSubmissions({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Form Submission
 */
export const deleteApiFormSubmissionsByIdMutation = (options?: Partial<Options<DeleteApiFormSubmissionsByIdData>>): UseMutationOptions<DeleteApiFormSubmissionsByIdResponse, DefaultError, Options<DeleteApiFormSubmissionsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiFormSubmissionsByIdResponse, DefaultError, Options<DeleteApiFormSubmissionsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiFormSubmissionsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiFormSubmissionsByIdQueryKey = (options: Options<GetApiFormSubmissionsByIdData>) => createQueryKey('getApiFormSubmissionsById', options);

/**
 * Find a Form Submission by ID
 */
export const getApiFormSubmissionsByIdOptions = (options: Options<GetApiFormSubmissionsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiFormSubmissionsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiFormSubmissionsByIdQueryKey(options)
    });
};

/**
 * Update a Form Submission
 */
export const patchApiFormSubmissionsByIdMutation = (options?: Partial<Options<PatchApiFormSubmissionsByIdData>>): UseMutationOptions<PatchApiFormSubmissionsByIdResponse, DefaultError, Options<PatchApiFormSubmissionsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiFormSubmissionsByIdResponse, DefaultError, Options<PatchApiFormSubmissionsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiFormSubmissionsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiSearchQueryKey = (options?: Options<GetApiSearchData>) => createQueryKey('getApiSearch', options);

/**
 * Retrieve a list of Search Results
 */
export const getApiSearchOptions = (options?: Options<GetApiSearchData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiSearch({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiSearchQueryKey(options)
    });
};

export const getApiSearchInfiniteQueryKey = (options?: Options<GetApiSearchData>): QueryKey<Options<GetApiSearchData>> => createQueryKey('getApiSearch', options, true);

/**
 * Retrieve a list of Search Results
 */
export const getApiSearchInfiniteOptions = (options?: Options<GetApiSearchData>) => {
    return infiniteQueryOptions<GetApiSearchResponse, DefaultError, InfiniteData<GetApiSearchResponse>, QueryKey<Options<GetApiSearchData>>, number | Pick<QueryKey<Options<GetApiSearchData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiSearchData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiSearch({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiSearchInfiniteQueryKey(options)
    });
};

export const postApiSearchQueryKey = (options?: Options<PostApiSearchData>) => createQueryKey('postApiSearch', options);

/**
 * Create a new Search Result
 */
export const postApiSearchOptions = (options?: Options<PostApiSearchData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiSearch({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiSearchQueryKey(options)
    });
};

/**
 * Create a new Search Result
 */
export const postApiSearchMutation = (options?: Partial<Options<PostApiSearchData>>): UseMutationOptions<PostApiSearchResponse, DefaultError, Options<PostApiSearchData>> => {
    const mutationOptions: UseMutationOptions<PostApiSearchResponse, DefaultError, Options<PostApiSearchData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiSearch({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Search Result
 */
export const deleteApiSearchByIdMutation = (options?: Partial<Options<DeleteApiSearchByIdData>>): UseMutationOptions<DeleteApiSearchByIdResponse, DefaultError, Options<DeleteApiSearchByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiSearchByIdResponse, DefaultError, Options<DeleteApiSearchByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiSearchById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiSearchByIdQueryKey = (options: Options<GetApiSearchByIdData>) => createQueryKey('getApiSearchById', options);

/**
 * Find a Search Result by ID
 */
export const getApiSearchByIdOptions = (options: Options<GetApiSearchByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiSearchById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiSearchByIdQueryKey(options)
    });
};

/**
 * Update a Search Result
 */
export const patchApiSearchByIdMutation = (options?: Partial<Options<PatchApiSearchByIdData>>): UseMutationOptions<PatchApiSearchByIdResponse, DefaultError, Options<PatchApiSearchByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiSearchByIdResponse, DefaultError, Options<PatchApiSearchByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiSearchById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPayloadJobsQueryKey = (options?: Options<GetApiPayloadJobsData>) => createQueryKey('getApiPayloadJobs', options);

/**
 * Retrieve a list of Payload Jobs
 */
export const getApiPayloadJobsOptions = (options?: Options<GetApiPayloadJobsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPayloadJobs({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadJobsQueryKey(options)
    });
};

export const getApiPayloadJobsInfiniteQueryKey = (options?: Options<GetApiPayloadJobsData>): QueryKey<Options<GetApiPayloadJobsData>> => createQueryKey('getApiPayloadJobs', options, true);

/**
 * Retrieve a list of Payload Jobs
 */
export const getApiPayloadJobsInfiniteOptions = (options?: Options<GetApiPayloadJobsData>) => {
    return infiniteQueryOptions<GetApiPayloadJobsResponse, DefaultError, InfiniteData<GetApiPayloadJobsResponse>, QueryKey<Options<GetApiPayloadJobsData>>, number | Pick<QueryKey<Options<GetApiPayloadJobsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiPayloadJobsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiPayloadJobs({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadJobsInfiniteQueryKey(options)
    });
};

export const postApiPayloadJobsQueryKey = (options?: Options<PostApiPayloadJobsData>) => createQueryKey('postApiPayloadJobs', options);

/**
 * Create a new Payload Job
 */
export const postApiPayloadJobsOptions = (options?: Options<PostApiPayloadJobsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiPayloadJobs({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiPayloadJobsQueryKey(options)
    });
};

/**
 * Create a new Payload Job
 */
export const postApiPayloadJobsMutation = (options?: Partial<Options<PostApiPayloadJobsData>>): UseMutationOptions<PostApiPayloadJobsResponse, DefaultError, Options<PostApiPayloadJobsData>> => {
    const mutationOptions: UseMutationOptions<PostApiPayloadJobsResponse, DefaultError, Options<PostApiPayloadJobsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiPayloadJobs({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Payload Job
 */
export const deleteApiPayloadJobsByIdMutation = (options?: Partial<Options<DeleteApiPayloadJobsByIdData>>): UseMutationOptions<DeleteApiPayloadJobsByIdResponse, DefaultError, Options<DeleteApiPayloadJobsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiPayloadJobsByIdResponse, DefaultError, Options<DeleteApiPayloadJobsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiPayloadJobsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPayloadJobsByIdQueryKey = (options: Options<GetApiPayloadJobsByIdData>) => createQueryKey('getApiPayloadJobsById', options);

/**
 * Find a Payload Job by ID
 */
export const getApiPayloadJobsByIdOptions = (options: Options<GetApiPayloadJobsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPayloadJobsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadJobsByIdQueryKey(options)
    });
};

/**
 * Update a Payload Job
 */
export const patchApiPayloadJobsByIdMutation = (options?: Partial<Options<PatchApiPayloadJobsByIdData>>): UseMutationOptions<PatchApiPayloadJobsByIdResponse, DefaultError, Options<PatchApiPayloadJobsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiPayloadJobsByIdResponse, DefaultError, Options<PatchApiPayloadJobsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiPayloadJobsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPayloadLockedDocumentsQueryKey = (options?: Options<GetApiPayloadLockedDocumentsData>) => createQueryKey('getApiPayloadLockedDocuments', options);

/**
 * Retrieve a list of Payload Locked Documents
 */
export const getApiPayloadLockedDocumentsOptions = (options?: Options<GetApiPayloadLockedDocumentsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPayloadLockedDocuments({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadLockedDocumentsQueryKey(options)
    });
};

export const getApiPayloadLockedDocumentsInfiniteQueryKey = (options?: Options<GetApiPayloadLockedDocumentsData>): QueryKey<Options<GetApiPayloadLockedDocumentsData>> => createQueryKey('getApiPayloadLockedDocuments', options, true);

/**
 * Retrieve a list of Payload Locked Documents
 */
export const getApiPayloadLockedDocumentsInfiniteOptions = (options?: Options<GetApiPayloadLockedDocumentsData>) => {
    return infiniteQueryOptions<GetApiPayloadLockedDocumentsResponse, DefaultError, InfiniteData<GetApiPayloadLockedDocumentsResponse>, QueryKey<Options<GetApiPayloadLockedDocumentsData>>, number | Pick<QueryKey<Options<GetApiPayloadLockedDocumentsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiPayloadLockedDocumentsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiPayloadLockedDocuments({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadLockedDocumentsInfiniteQueryKey(options)
    });
};

export const postApiPayloadLockedDocumentsQueryKey = (options?: Options<PostApiPayloadLockedDocumentsData>) => createQueryKey('postApiPayloadLockedDocuments', options);

/**
 * Create a new Payload Locked Document
 */
export const postApiPayloadLockedDocumentsOptions = (options?: Options<PostApiPayloadLockedDocumentsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiPayloadLockedDocuments({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiPayloadLockedDocumentsQueryKey(options)
    });
};

/**
 * Create a new Payload Locked Document
 */
export const postApiPayloadLockedDocumentsMutation = (options?: Partial<Options<PostApiPayloadLockedDocumentsData>>): UseMutationOptions<PostApiPayloadLockedDocumentsResponse, DefaultError, Options<PostApiPayloadLockedDocumentsData>> => {
    const mutationOptions: UseMutationOptions<PostApiPayloadLockedDocumentsResponse, DefaultError, Options<PostApiPayloadLockedDocumentsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiPayloadLockedDocuments({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Payload Locked Document
 */
export const deleteApiPayloadLockedDocumentsByIdMutation = (options?: Partial<Options<DeleteApiPayloadLockedDocumentsByIdData>>): UseMutationOptions<DeleteApiPayloadLockedDocumentsByIdResponse, DefaultError, Options<DeleteApiPayloadLockedDocumentsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiPayloadLockedDocumentsByIdResponse, DefaultError, Options<DeleteApiPayloadLockedDocumentsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiPayloadLockedDocumentsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPayloadLockedDocumentsByIdQueryKey = (options: Options<GetApiPayloadLockedDocumentsByIdData>) => createQueryKey('getApiPayloadLockedDocumentsById', options);

/**
 * Find a Payload Locked Document by ID
 */
export const getApiPayloadLockedDocumentsByIdOptions = (options: Options<GetApiPayloadLockedDocumentsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPayloadLockedDocumentsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadLockedDocumentsByIdQueryKey(options)
    });
};

/**
 * Update a Payload Locked Document
 */
export const patchApiPayloadLockedDocumentsByIdMutation = (options?: Partial<Options<PatchApiPayloadLockedDocumentsByIdData>>): UseMutationOptions<PatchApiPayloadLockedDocumentsByIdResponse, DefaultError, Options<PatchApiPayloadLockedDocumentsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiPayloadLockedDocumentsByIdResponse, DefaultError, Options<PatchApiPayloadLockedDocumentsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiPayloadLockedDocumentsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPayloadPreferencesQueryKey = (options?: Options<GetApiPayloadPreferencesData>) => createQueryKey('getApiPayloadPreferences', options);

/**
 * Retrieve a list of Payload Preferences
 */
export const getApiPayloadPreferencesOptions = (options?: Options<GetApiPayloadPreferencesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPayloadPreferences({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadPreferencesQueryKey(options)
    });
};

export const getApiPayloadPreferencesInfiniteQueryKey = (options?: Options<GetApiPayloadPreferencesData>): QueryKey<Options<GetApiPayloadPreferencesData>> => createQueryKey('getApiPayloadPreferences', options, true);

/**
 * Retrieve a list of Payload Preferences
 */
export const getApiPayloadPreferencesInfiniteOptions = (options?: Options<GetApiPayloadPreferencesData>) => {
    return infiniteQueryOptions<GetApiPayloadPreferencesResponse, DefaultError, InfiniteData<GetApiPayloadPreferencesResponse>, QueryKey<Options<GetApiPayloadPreferencesData>>, number | Pick<QueryKey<Options<GetApiPayloadPreferencesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiPayloadPreferencesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiPayloadPreferences({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadPreferencesInfiniteQueryKey(options)
    });
};

export const postApiPayloadPreferencesQueryKey = (options?: Options<PostApiPayloadPreferencesData>) => createQueryKey('postApiPayloadPreferences', options);

/**
 * Create a new Payload Preference
 */
export const postApiPayloadPreferencesOptions = (options?: Options<PostApiPayloadPreferencesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiPayloadPreferences({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiPayloadPreferencesQueryKey(options)
    });
};

/**
 * Create a new Payload Preference
 */
export const postApiPayloadPreferencesMutation = (options?: Partial<Options<PostApiPayloadPreferencesData>>): UseMutationOptions<PostApiPayloadPreferencesResponse, DefaultError, Options<PostApiPayloadPreferencesData>> => {
    const mutationOptions: UseMutationOptions<PostApiPayloadPreferencesResponse, DefaultError, Options<PostApiPayloadPreferencesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiPayloadPreferences({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Payload Preference
 */
export const deleteApiPayloadPreferencesByIdMutation = (options?: Partial<Options<DeleteApiPayloadPreferencesByIdData>>): UseMutationOptions<DeleteApiPayloadPreferencesByIdResponse, DefaultError, Options<DeleteApiPayloadPreferencesByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiPayloadPreferencesByIdResponse, DefaultError, Options<DeleteApiPayloadPreferencesByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiPayloadPreferencesById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPayloadPreferencesByIdQueryKey = (options: Options<GetApiPayloadPreferencesByIdData>) => createQueryKey('getApiPayloadPreferencesById', options);

/**
 * Find a Payload Preference by ID
 */
export const getApiPayloadPreferencesByIdOptions = (options: Options<GetApiPayloadPreferencesByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPayloadPreferencesById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadPreferencesByIdQueryKey(options)
    });
};

/**
 * Update a Payload Preference
 */
export const patchApiPayloadPreferencesByIdMutation = (options?: Partial<Options<PatchApiPayloadPreferencesByIdData>>): UseMutationOptions<PatchApiPayloadPreferencesByIdResponse, DefaultError, Options<PatchApiPayloadPreferencesByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiPayloadPreferencesByIdResponse, DefaultError, Options<PatchApiPayloadPreferencesByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiPayloadPreferencesById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPayloadMigrationsQueryKey = (options?: Options<GetApiPayloadMigrationsData>) => createQueryKey('getApiPayloadMigrations', options);

/**
 * Retrieve a list of Payload Migrations
 */
export const getApiPayloadMigrationsOptions = (options?: Options<GetApiPayloadMigrationsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPayloadMigrations({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadMigrationsQueryKey(options)
    });
};

export const getApiPayloadMigrationsInfiniteQueryKey = (options?: Options<GetApiPayloadMigrationsData>): QueryKey<Options<GetApiPayloadMigrationsData>> => createQueryKey('getApiPayloadMigrations', options, true);

/**
 * Retrieve a list of Payload Migrations
 */
export const getApiPayloadMigrationsInfiniteOptions = (options?: Options<GetApiPayloadMigrationsData>) => {
    return infiniteQueryOptions<GetApiPayloadMigrationsResponse, DefaultError, InfiniteData<GetApiPayloadMigrationsResponse>, QueryKey<Options<GetApiPayloadMigrationsData>>, number | Pick<QueryKey<Options<GetApiPayloadMigrationsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiPayloadMigrationsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiPayloadMigrations({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadMigrationsInfiniteQueryKey(options)
    });
};

export const postApiPayloadMigrationsQueryKey = (options?: Options<PostApiPayloadMigrationsData>) => createQueryKey('postApiPayloadMigrations', options);

/**
 * Create a new Payload Migration
 */
export const postApiPayloadMigrationsOptions = (options?: Options<PostApiPayloadMigrationsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiPayloadMigrations({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiPayloadMigrationsQueryKey(options)
    });
};

/**
 * Create a new Payload Migration
 */
export const postApiPayloadMigrationsMutation = (options?: Partial<Options<PostApiPayloadMigrationsData>>): UseMutationOptions<PostApiPayloadMigrationsResponse, DefaultError, Options<PostApiPayloadMigrationsData>> => {
    const mutationOptions: UseMutationOptions<PostApiPayloadMigrationsResponse, DefaultError, Options<PostApiPayloadMigrationsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiPayloadMigrations({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Payload Migration
 */
export const deleteApiPayloadMigrationsByIdMutation = (options?: Partial<Options<DeleteApiPayloadMigrationsByIdData>>): UseMutationOptions<DeleteApiPayloadMigrationsByIdResponse, DefaultError, Options<DeleteApiPayloadMigrationsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiPayloadMigrationsByIdResponse, DefaultError, Options<DeleteApiPayloadMigrationsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiPayloadMigrationsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiPayloadMigrationsByIdQueryKey = (options: Options<GetApiPayloadMigrationsByIdData>) => createQueryKey('getApiPayloadMigrationsById', options);

/**
 * Find a Payload Migration by ID
 */
export const getApiPayloadMigrationsByIdOptions = (options: Options<GetApiPayloadMigrationsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiPayloadMigrationsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiPayloadMigrationsByIdQueryKey(options)
    });
};

/**
 * Update a Payload Migration
 */
export const patchApiPayloadMigrationsByIdMutation = (options?: Partial<Options<PatchApiPayloadMigrationsByIdData>>): UseMutationOptions<PatchApiPayloadMigrationsByIdResponse, DefaultError, Options<PatchApiPayloadMigrationsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiPayloadMigrationsByIdResponse, DefaultError, Options<PatchApiPayloadMigrationsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiPayloadMigrationsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiGlobalsHeaderQueryKey = (options?: Options<GetApiGlobalsHeaderData>) => createQueryKey('getApiGlobalsHeader', options);

/**
 * Get the Header
 */
export const getApiGlobalsHeaderOptions = (options?: Options<GetApiGlobalsHeaderData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiGlobalsHeader({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiGlobalsHeaderQueryKey(options)
    });
};

export const postApiGlobalsHeaderQueryKey = (options?: Options<PostApiGlobalsHeaderData>) => createQueryKey('postApiGlobalsHeader', options);

/**
 * Update the Header
 */
export const postApiGlobalsHeaderOptions = (options?: Options<PostApiGlobalsHeaderData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiGlobalsHeader({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiGlobalsHeaderQueryKey(options)
    });
};

/**
 * Update the Header
 */
export const postApiGlobalsHeaderMutation = (options?: Partial<Options<PostApiGlobalsHeaderData>>): UseMutationOptions<PostApiGlobalsHeaderResponse, DefaultError, Options<PostApiGlobalsHeaderData>> => {
    const mutationOptions: UseMutationOptions<PostApiGlobalsHeaderResponse, DefaultError, Options<PostApiGlobalsHeaderData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiGlobalsHeader({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiGlobalsFooterQueryKey = (options?: Options<GetApiGlobalsFooterData>) => createQueryKey('getApiGlobalsFooter', options);

/**
 * Get the Footer
 */
export const getApiGlobalsFooterOptions = (options?: Options<GetApiGlobalsFooterData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiGlobalsFooter({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiGlobalsFooterQueryKey(options)
    });
};

export const postApiGlobalsFooterQueryKey = (options?: Options<PostApiGlobalsFooterData>) => createQueryKey('postApiGlobalsFooter', options);

/**
 * Update the Footer
 */
export const postApiGlobalsFooterOptions = (options?: Options<PostApiGlobalsFooterData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiGlobalsFooter({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiGlobalsFooterQueryKey(options)
    });
};

/**
 * Update the Footer
 */
export const postApiGlobalsFooterMutation = (options?: Partial<Options<PostApiGlobalsFooterData>>): UseMutationOptions<PostApiGlobalsFooterResponse, DefaultError, Options<PostApiGlobalsFooterData>> => {
    const mutationOptions: UseMutationOptions<PostApiGlobalsFooterResponse, DefaultError, Options<PostApiGlobalsFooterData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiGlobalsFooter({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};