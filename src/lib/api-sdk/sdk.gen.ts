// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from './client';
import type { GetApiPagesData, GetApiPagesResponses, PostApiPagesData, PostApiPagesResponses, DeleteApiPagesByIdData, DeleteApiPagesByIdResponses, DeleteApiPagesByIdErrors, GetApiPagesByIdData, GetApiPagesByIdResponses, GetApiPagesByIdErrors, PatchApiPagesByIdData, PatchApiPagesByIdResponses, PatchApiPagesByIdErrors, GetApiPostsData, GetApiPostsResponses, PostApiPostsData, PostApiPostsResponses, DeleteApiPostsByIdData, DeleteApiPostsByIdResponses, DeleteApiPostsByIdErrors, GetApiPostsByIdData, GetApiPostsByIdResponses, GetApiPostsByIdErrors, PatchApiPostsByIdData, PatchApiPostsByIdResponses, PatchApiPostsByIdErrors, GetApiMediaData, GetApiMediaResponses, PostApiMediaData, PostApiMediaResponses, DeleteApiMediaByIdData, DeleteApiMediaByIdResponses, DeleteApiMediaByIdErrors, GetApiMediaByIdData, GetApiMediaByIdResponses, GetApiMediaByIdErrors, PatchApiMediaByIdData, PatchApiMediaByIdResponses, PatchApiMediaByIdErrors, GetApiCategoriesData, GetApiCategoriesResponses, PostApiCategoriesData, PostApiCategoriesResponses, DeleteApiCategoriesByIdData, DeleteApiCategoriesByIdResponses, DeleteApiCategoriesByIdErrors, GetApiCategoriesByIdData, GetApiCategoriesByIdResponses, GetApiCategoriesByIdErrors, PatchApiCategoriesByIdData, PatchApiCategoriesByIdResponses, PatchApiCategoriesByIdErrors, GetApiUsersData, GetApiUsersResponses, PostApiUsersData, PostApiUsersResponses, DeleteApiUsersByIdData, DeleteApiUsersByIdResponses, DeleteApiUsersByIdErrors, GetApiUsersByIdData, GetApiUsersByIdResponses, GetApiUsersByIdErrors, PatchApiUsersByIdData, PatchApiUsersByIdResponses, PatchApiUsersByIdErrors, GetApiTeamsData, GetApiTeamsResponses, PostApiTeamsData, PostApiTeamsResponses, DeleteApiTeamsByIdData, DeleteApiTeamsByIdResponses, DeleteApiTeamsByIdErrors, GetApiTeamsByIdData, GetApiTeamsByIdResponses, GetApiTeamsByIdErrors, PatchApiTeamsByIdData, PatchApiTeamsByIdResponses, PatchApiTeamsByIdErrors, GetApiProjectsData, GetApiProjectsResponses, PostApiProjectsData, PostApiProjectsResponses, DeleteApiProjectsByIdData, DeleteApiProjectsByIdResponses, DeleteApiProjectsByIdErrors, GetApiProjectsByIdData, GetApiProjectsByIdResponses, GetApiProjectsByIdErrors, PatchApiProjectsByIdData, PatchApiProjectsByIdResponses, PatchApiProjectsByIdErrors, GetApiKeywordsData, GetApiKeywordsResponses, PostApiKeywordsData, PostApiKeywordsResponses, DeleteApiKeywordsByIdData, DeleteApiKeywordsByIdResponses, DeleteApiKeywordsByIdErrors, GetApiKeywordsByIdData, GetApiKeywordsByIdResponses, GetApiKeywordsByIdErrors, PatchApiKeywordsByIdData, PatchApiKeywordsByIdResponses, PatchApiKeywordsByIdErrors, GetApiAssetsData, GetApiAssetsResponses, PostApiAssetsData, PostApiAssetsResponses, DeleteApiAssetsByIdData, DeleteApiAssetsByIdResponses, DeleteApiAssetsByIdErrors, GetApiAssetsByIdData, GetApiAssetsByIdResponses, GetApiAssetsByIdErrors, PatchApiAssetsByIdData, PatchApiAssetsByIdResponses, PatchApiAssetsByIdErrors, GetApiScreenshotsData, GetApiScreenshotsResponses, PostApiScreenshotsData, PostApiScreenshotsResponses, DeleteApiScreenshotsByIdData, DeleteApiScreenshotsByIdResponses, DeleteApiScreenshotsByIdErrors, GetApiScreenshotsByIdData, GetApiScreenshotsByIdResponses, GetApiScreenshotsByIdErrors, PatchApiScreenshotsByIdData, PatchApiScreenshotsByIdResponses, PatchApiScreenshotsByIdErrors, GetApiProductsData, GetApiProductsResponses, PostApiProductsData, PostApiProductsResponses, DeleteApiProductsByIdData, DeleteApiProductsByIdResponses, DeleteApiProductsByIdErrors, GetApiProductsByIdData, GetApiProductsByIdResponses, GetApiProductsByIdErrors, PatchApiProductsByIdData, PatchApiProductsByIdResponses, PatchApiProductsByIdErrors, GetApiSubscriptionsData, GetApiSubscriptionsResponses, PostApiSubscriptionsData, PostApiSubscriptionsResponses, DeleteApiSubscriptionsByIdData, DeleteApiSubscriptionsByIdResponses, DeleteApiSubscriptionsByIdErrors, GetApiSubscriptionsByIdData, GetApiSubscriptionsByIdResponses, GetApiSubscriptionsByIdErrors, PatchApiSubscriptionsByIdData, PatchApiSubscriptionsByIdResponses, PatchApiSubscriptionsByIdErrors, GetApiRedirectsData, GetApiRedirectsResponses, PostApiRedirectsData, PostApiRedirectsResponses, DeleteApiRedirectsByIdData, DeleteApiRedirectsByIdResponses, DeleteApiRedirectsByIdErrors, GetApiRedirectsByIdData, GetApiRedirectsByIdResponses, GetApiRedirectsByIdErrors, PatchApiRedirectsByIdData, PatchApiRedirectsByIdResponses, PatchApiRedirectsByIdErrors, GetApiFormsData, GetApiFormsResponses, PostApiFormsData, PostApiFormsResponses, DeleteApiFormsByIdData, DeleteApiFormsByIdResponses, DeleteApiFormsByIdErrors, GetApiFormsByIdData, GetApiFormsByIdResponses, GetApiFormsByIdErrors, PatchApiFormsByIdData, PatchApiFormsByIdResponses, PatchApiFormsByIdErrors, GetApiFormSubmissionsData, GetApiFormSubmissionsResponses, PostApiFormSubmissionsData, PostApiFormSubmissionsResponses, DeleteApiFormSubmissionsByIdData, DeleteApiFormSubmissionsByIdResponses, DeleteApiFormSubmissionsByIdErrors, GetApiFormSubmissionsByIdData, GetApiFormSubmissionsByIdResponses, GetApiFormSubmissionsByIdErrors, PatchApiFormSubmissionsByIdData, PatchApiFormSubmissionsByIdResponses, PatchApiFormSubmissionsByIdErrors, GetApiSearchData, GetApiSearchResponses, PostApiSearchData, PostApiSearchResponses, DeleteApiSearchByIdData, DeleteApiSearchByIdResponses, DeleteApiSearchByIdErrors, GetApiSearchByIdData, GetApiSearchByIdResponses, GetApiSearchByIdErrors, PatchApiSearchByIdData, PatchApiSearchByIdResponses, PatchApiSearchByIdErrors, GetApiPayloadJobsData, GetApiPayloadJobsResponses, PostApiPayloadJobsData, PostApiPayloadJobsResponses, DeleteApiPayloadJobsByIdData, DeleteApiPayloadJobsByIdResponses, DeleteApiPayloadJobsByIdErrors, GetApiPayloadJobsByIdData, GetApiPayloadJobsByIdResponses, GetApiPayloadJobsByIdErrors, PatchApiPayloadJobsByIdData, PatchApiPayloadJobsByIdResponses, PatchApiPayloadJobsByIdErrors, GetApiPayloadLockedDocumentsData, GetApiPayloadLockedDocumentsResponses, PostApiPayloadLockedDocumentsData, PostApiPayloadLockedDocumentsResponses, DeleteApiPayloadLockedDocumentsByIdData, DeleteApiPayloadLockedDocumentsByIdResponses, DeleteApiPayloadLockedDocumentsByIdErrors, GetApiPayloadLockedDocumentsByIdData, GetApiPayloadLockedDocumentsByIdResponses, GetApiPayloadLockedDocumentsByIdErrors, PatchApiPayloadLockedDocumentsByIdData, PatchApiPayloadLockedDocumentsByIdResponses, PatchApiPayloadLockedDocumentsByIdErrors, GetApiPayloadPreferencesData, GetApiPayloadPreferencesResponses, PostApiPayloadPreferencesData, PostApiPayloadPreferencesResponses, DeleteApiPayloadPreferencesByIdData, DeleteApiPayloadPreferencesByIdResponses, DeleteApiPayloadPreferencesByIdErrors, GetApiPayloadPreferencesByIdData, GetApiPayloadPreferencesByIdResponses, GetApiPayloadPreferencesByIdErrors, PatchApiPayloadPreferencesByIdData, PatchApiPayloadPreferencesByIdResponses, PatchApiPayloadPreferencesByIdErrors, GetApiPayloadMigrationsData, GetApiPayloadMigrationsResponses, PostApiPayloadMigrationsData, PostApiPayloadMigrationsResponses, DeleteApiPayloadMigrationsByIdData, DeleteApiPayloadMigrationsByIdResponses, DeleteApiPayloadMigrationsByIdErrors, GetApiPayloadMigrationsByIdData, GetApiPayloadMigrationsByIdResponses, GetApiPayloadMigrationsByIdErrors, PatchApiPayloadMigrationsByIdData, PatchApiPayloadMigrationsByIdResponses, PatchApiPayloadMigrationsByIdErrors, PostApiUsersLoginData, PostApiUsersLoginResponses, PostApiUsersLoginErrors, PostApiUsersLogoutData, PostApiUsersLogoutResponses, PostApiUsersRefreshTokenData, PostApiUsersRefreshTokenResponses, PostApiUsersRefreshTokenErrors, GetApiUsersMeData, GetApiUsersMeResponses, GetApiUsersMeErrors, PostApiUsersForgotPasswordData, PostApiUsersForgotPasswordResponses, PostApiUsersForgotPasswordErrors, PostApiUsersResetPasswordData, PostApiUsersResetPasswordResponses, PostApiUsersResetPasswordErrors, PostApiUsersVerifyByTokenData, PostApiUsersVerifyByTokenResponses, PostApiUsersVerifyByTokenErrors, PostApiUsersUnlockData, PostApiUsersUnlockResponses, PostApiUsersUnlockErrors, GetApiGlobalsHeaderData, GetApiGlobalsHeaderResponses, PostApiGlobalsHeaderData, PostApiGlobalsHeaderResponses, GetApiGlobalsFooterData, GetApiGlobalsFooterResponses, PostApiGlobalsFooterData, PostApiGlobalsFooterResponses } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Retrieve a list of Pages
 */
export const getApiPages = <ThrowOnError extends boolean = false>(options?: Options<GetApiPagesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPagesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/pages',
        ...options
    });
};

/**
 * Create a new Page
 */
export const postApiPages = <ThrowOnError extends boolean = false>(options?: Options<PostApiPagesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPagesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/pages',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Page
 */
export const deleteApiPagesById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiPagesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiPagesByIdResponses, DeleteApiPagesByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/pages/{id}',
        ...options
    });
};

/**
 * Find a Page by ID
 */
export const getApiPagesById = <ThrowOnError extends boolean = false>(options: Options<GetApiPagesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPagesByIdResponses, GetApiPagesByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/pages/{id}',
        ...options
    });
};

/**
 * Update a Page
 */
export const patchApiPagesById = <ThrowOnError extends boolean = false>(options: Options<PatchApiPagesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiPagesByIdResponses, PatchApiPagesByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/pages/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Posts
 */
export const getApiPosts = <ThrowOnError extends boolean = false>(options?: Options<GetApiPostsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPostsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/posts',
        ...options
    });
};

/**
 * Create a new Post
 */
export const postApiPosts = <ThrowOnError extends boolean = false>(options?: Options<PostApiPostsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPostsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/posts',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Post
 */
export const deleteApiPostsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiPostsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiPostsByIdResponses, DeleteApiPostsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/posts/{id}',
        ...options
    });
};

/**
 * Find a Post by ID
 */
export const getApiPostsById = <ThrowOnError extends boolean = false>(options: Options<GetApiPostsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPostsByIdResponses, GetApiPostsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/posts/{id}',
        ...options
    });
};

/**
 * Update a Post
 */
export const patchApiPostsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiPostsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiPostsByIdResponses, PatchApiPostsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/posts/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Media
 */
export const getApiMedia = <ThrowOnError extends boolean = false>(options?: Options<GetApiMediaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMediaResponses, unknown, ThrowOnError>({
        url: '/api/media',
        ...options
    });
};

/**
 * Create a new Media
 */
export const postApiMedia = <ThrowOnError extends boolean = false>(options?: Options<PostApiMediaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMediaResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/media',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Media
 */
export const deleteApiMediaById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMediaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMediaByIdResponses, DeleteApiMediaByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/media/{id}',
        ...options
    });
};

/**
 * Find a Media by ID
 */
export const getApiMediaById = <ThrowOnError extends boolean = false>(options: Options<GetApiMediaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMediaByIdResponses, GetApiMediaByIdErrors, ThrowOnError>({
        url: '/api/media/{id}',
        ...options
    });
};

/**
 * Update a Media
 */
export const patchApiMediaById = <ThrowOnError extends boolean = false>(options: Options<PatchApiMediaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiMediaByIdResponses, PatchApiMediaByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/media/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Categories
 */
export const getApiCategories = <ThrowOnError extends boolean = false>(options?: Options<GetApiCategoriesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiCategoriesResponses, unknown, ThrowOnError>({
        url: '/api/categories',
        ...options
    });
};

/**
 * Create a new Category
 */
export const postApiCategories = <ThrowOnError extends boolean = false>(options?: Options<PostApiCategoriesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiCategoriesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/categories',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Category
 */
export const deleteApiCategoriesById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiCategoriesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiCategoriesByIdResponses, DeleteApiCategoriesByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/categories/{id}',
        ...options
    });
};

/**
 * Find a Category by ID
 */
export const getApiCategoriesById = <ThrowOnError extends boolean = false>(options: Options<GetApiCategoriesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiCategoriesByIdResponses, GetApiCategoriesByIdErrors, ThrowOnError>({
        url: '/api/categories/{id}',
        ...options
    });
};

/**
 * Update a Category
 */
export const patchApiCategoriesById = <ThrowOnError extends boolean = false>(options: Options<PatchApiCategoriesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiCategoriesByIdResponses, PatchApiCategoriesByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/categories/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Users
 */
export const getApiUsers = <ThrowOnError extends boolean = false>(options?: Options<GetApiUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiUsersResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users',
        ...options
    });
};

/**
 * Create a new User
 */
export const postApiUsers = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a User
 */
export const deleteApiUsersById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiUsersByIdResponses, DeleteApiUsersByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/{id}',
        ...options
    });
};

/**
 * Find a User by ID
 */
export const getApiUsersById = <ThrowOnError extends boolean = false>(options: Options<GetApiUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiUsersByIdResponses, GetApiUsersByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/{id}',
        ...options
    });
};

/**
 * Update a User
 */
export const patchApiUsersById = <ThrowOnError extends boolean = false>(options: Options<PatchApiUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiUsersByIdResponses, PatchApiUsersByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Teams
 */
export const getApiTeams = <ThrowOnError extends boolean = false>(options?: Options<GetApiTeamsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTeamsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/teams',
        ...options
    });
};

/**
 * Create a new Team
 */
export const postApiTeams = <ThrowOnError extends boolean = false>(options?: Options<PostApiTeamsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTeamsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/teams',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Team
 */
export const deleteApiTeamsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiTeamsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiTeamsByIdResponses, DeleteApiTeamsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/teams/{id}',
        ...options
    });
};

/**
 * Find a Team by ID
 */
export const getApiTeamsById = <ThrowOnError extends boolean = false>(options: Options<GetApiTeamsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiTeamsByIdResponses, GetApiTeamsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/teams/{id}',
        ...options
    });
};

/**
 * Update a Team
 */
export const patchApiTeamsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiTeamsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiTeamsByIdResponses, PatchApiTeamsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/teams/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Projects
 */
export const getApiProjects = <ThrowOnError extends boolean = false>(options?: Options<GetApiProjectsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiProjectsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/projects',
        ...options
    });
};

/**
 * Create a new Project
 */
export const postApiProjects = <ThrowOnError extends boolean = false>(options?: Options<PostApiProjectsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiProjectsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/projects',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Project
 */
export const deleteApiProjectsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiProjectsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiProjectsByIdResponses, DeleteApiProjectsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/projects/{id}',
        ...options
    });
};

/**
 * Find a Project by ID
 */
export const getApiProjectsById = <ThrowOnError extends boolean = false>(options: Options<GetApiProjectsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiProjectsByIdResponses, GetApiProjectsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/projects/{id}',
        ...options
    });
};

/**
 * Update a Project
 */
export const patchApiProjectsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiProjectsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiProjectsByIdResponses, PatchApiProjectsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/projects/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Keywords
 */
export const getApiKeywords = <ThrowOnError extends boolean = false>(options?: Options<GetApiKeywordsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiKeywordsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/keywords',
        ...options
    });
};

/**
 * Create a new Keyword
 */
export const postApiKeywords = <ThrowOnError extends boolean = false>(options?: Options<PostApiKeywordsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiKeywordsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/keywords',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Keyword
 */
export const deleteApiKeywordsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiKeywordsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiKeywordsByIdResponses, DeleteApiKeywordsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/keywords/{id}',
        ...options
    });
};

/**
 * Find a Keyword by ID
 */
export const getApiKeywordsById = <ThrowOnError extends boolean = false>(options: Options<GetApiKeywordsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiKeywordsByIdResponses, GetApiKeywordsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/keywords/{id}',
        ...options
    });
};

/**
 * Update a Keyword
 */
export const patchApiKeywordsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiKeywordsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiKeywordsByIdResponses, PatchApiKeywordsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/keywords/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Assets
 */
export const getApiAssets = <ThrowOnError extends boolean = false>(options?: Options<GetApiAssetsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAssetsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/assets',
        ...options
    });
};

/**
 * Create a new Asset
 */
export const postApiAssets = <ThrowOnError extends boolean = false>(options?: Options<PostApiAssetsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAssetsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/assets',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Asset
 */
export const deleteApiAssetsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiAssetsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiAssetsByIdResponses, DeleteApiAssetsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/assets/{id}',
        ...options
    });
};

/**
 * Find a Asset by ID
 */
export const getApiAssetsById = <ThrowOnError extends boolean = false>(options: Options<GetApiAssetsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAssetsByIdResponses, GetApiAssetsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/assets/{id}',
        ...options
    });
};

/**
 * Update a Asset
 */
export const patchApiAssetsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiAssetsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiAssetsByIdResponses, PatchApiAssetsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/assets/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Screenshots
 */
export const getApiScreenshots = <ThrowOnError extends boolean = false>(options?: Options<GetApiScreenshotsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiScreenshotsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/screenshots',
        ...options
    });
};

/**
 * Create a new Screenshot
 */
export const postApiScreenshots = <ThrowOnError extends boolean = false>(options?: Options<PostApiScreenshotsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiScreenshotsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/screenshots',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Screenshot
 */
export const deleteApiScreenshotsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiScreenshotsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiScreenshotsByIdResponses, DeleteApiScreenshotsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/screenshots/{id}',
        ...options
    });
};

/**
 * Find a Screenshot by ID
 */
export const getApiScreenshotsById = <ThrowOnError extends boolean = false>(options: Options<GetApiScreenshotsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiScreenshotsByIdResponses, GetApiScreenshotsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/screenshots/{id}',
        ...options
    });
};

/**
 * Update a Screenshot
 */
export const patchApiScreenshotsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiScreenshotsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiScreenshotsByIdResponses, PatchApiScreenshotsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/screenshots/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Products
 */
export const getApiProducts = <ThrowOnError extends boolean = false>(options?: Options<GetApiProductsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiProductsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/products',
        ...options
    });
};

/**
 * Create a new Product
 */
export const postApiProducts = <ThrowOnError extends boolean = false>(options?: Options<PostApiProductsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiProductsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/products',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Product
 */
export const deleteApiProductsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiProductsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiProductsByIdResponses, DeleteApiProductsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/products/{id}',
        ...options
    });
};

/**
 * Find a Product by ID
 */
export const getApiProductsById = <ThrowOnError extends boolean = false>(options: Options<GetApiProductsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiProductsByIdResponses, GetApiProductsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/products/{id}',
        ...options
    });
};

/**
 * Update a Product
 */
export const patchApiProductsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiProductsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiProductsByIdResponses, PatchApiProductsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/products/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Subscriptions
 */
export const getApiSubscriptions = <ThrowOnError extends boolean = false>(options?: Options<GetApiSubscriptionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiSubscriptionsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/subscriptions',
        ...options
    });
};

/**
 * Create a new Subscription
 */
export const postApiSubscriptions = <ThrowOnError extends boolean = false>(options?: Options<PostApiSubscriptionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSubscriptionsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/subscriptions',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Subscription
 */
export const deleteApiSubscriptionsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiSubscriptionsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiSubscriptionsByIdResponses, DeleteApiSubscriptionsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/subscriptions/{id}',
        ...options
    });
};

/**
 * Find a Subscription by ID
 */
export const getApiSubscriptionsById = <ThrowOnError extends boolean = false>(options: Options<GetApiSubscriptionsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiSubscriptionsByIdResponses, GetApiSubscriptionsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/subscriptions/{id}',
        ...options
    });
};

/**
 * Update a Subscription
 */
export const patchApiSubscriptionsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiSubscriptionsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiSubscriptionsByIdResponses, PatchApiSubscriptionsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/subscriptions/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Redirects
 */
export const getApiRedirects = <ThrowOnError extends boolean = false>(options?: Options<GetApiRedirectsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiRedirectsResponses, unknown, ThrowOnError>({
        url: '/api/redirects',
        ...options
    });
};

/**
 * Create a new Redirect
 */
export const postApiRedirects = <ThrowOnError extends boolean = false>(options?: Options<PostApiRedirectsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiRedirectsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/redirects',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Redirect
 */
export const deleteApiRedirectsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiRedirectsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiRedirectsByIdResponses, DeleteApiRedirectsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/redirects/{id}',
        ...options
    });
};

/**
 * Find a Redirect by ID
 */
export const getApiRedirectsById = <ThrowOnError extends boolean = false>(options: Options<GetApiRedirectsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiRedirectsByIdResponses, GetApiRedirectsByIdErrors, ThrowOnError>({
        url: '/api/redirects/{id}',
        ...options
    });
};

/**
 * Update a Redirect
 */
export const patchApiRedirectsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiRedirectsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiRedirectsByIdResponses, PatchApiRedirectsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/redirects/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Forms
 */
export const getApiForms = <ThrowOnError extends boolean = false>(options?: Options<GetApiFormsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiFormsResponses, unknown, ThrowOnError>({
        url: '/api/forms',
        ...options
    });
};

/**
 * Create a new Form
 */
export const postApiForms = <ThrowOnError extends boolean = false>(options?: Options<PostApiFormsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiFormsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/forms',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Form
 */
export const deleteApiFormsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiFormsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiFormsByIdResponses, DeleteApiFormsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/forms/{id}',
        ...options
    });
};

/**
 * Find a Form by ID
 */
export const getApiFormsById = <ThrowOnError extends boolean = false>(options: Options<GetApiFormsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiFormsByIdResponses, GetApiFormsByIdErrors, ThrowOnError>({
        url: '/api/forms/{id}',
        ...options
    });
};

/**
 * Update a Form
 */
export const patchApiFormsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiFormsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiFormsByIdResponses, PatchApiFormsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/forms/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Form Submissions
 */
export const getApiFormSubmissions = <ThrowOnError extends boolean = false>(options?: Options<GetApiFormSubmissionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiFormSubmissionsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/form-submissions',
        ...options
    });
};

/**
 * Create a new Form Submission
 */
export const postApiFormSubmissions = <ThrowOnError extends boolean = false>(options?: Options<PostApiFormSubmissionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiFormSubmissionsResponses, unknown, ThrowOnError>({
        url: '/api/form-submissions',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Form Submission
 */
export const deleteApiFormSubmissionsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiFormSubmissionsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiFormSubmissionsByIdResponses, DeleteApiFormSubmissionsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/form-submissions/{id}',
        ...options
    });
};

/**
 * Find a Form Submission by ID
 */
export const getApiFormSubmissionsById = <ThrowOnError extends boolean = false>(options: Options<GetApiFormSubmissionsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiFormSubmissionsByIdResponses, GetApiFormSubmissionsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/form-submissions/{id}',
        ...options
    });
};

/**
 * Update a Form Submission
 */
export const patchApiFormSubmissionsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiFormSubmissionsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiFormSubmissionsByIdResponses, PatchApiFormSubmissionsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/form-submissions/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Search Results
 */
export const getApiSearch = <ThrowOnError extends boolean = false>(options?: Options<GetApiSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiSearchResponses, unknown, ThrowOnError>({
        url: '/api/search',
        ...options
    });
};

/**
 * Create a new Search Result
 */
export const postApiSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSearchResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Search Result
 */
export const deleteApiSearchById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiSearchByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiSearchByIdResponses, DeleteApiSearchByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/search/{id}',
        ...options
    });
};

/**
 * Find a Search Result by ID
 */
export const getApiSearchById = <ThrowOnError extends boolean = false>(options: Options<GetApiSearchByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiSearchByIdResponses, GetApiSearchByIdErrors, ThrowOnError>({
        url: '/api/search/{id}',
        ...options
    });
};

/**
 * Update a Search Result
 */
export const patchApiSearchById = <ThrowOnError extends boolean = false>(options: Options<PatchApiSearchByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiSearchByIdResponses, PatchApiSearchByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/search/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Payload Jobs
 */
export const getApiPayloadJobs = <ThrowOnError extends boolean = false>(options?: Options<GetApiPayloadJobsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPayloadJobsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-jobs',
        ...options
    });
};

/**
 * Create a new Payload Job
 */
export const postApiPayloadJobs = <ThrowOnError extends boolean = false>(options?: Options<PostApiPayloadJobsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPayloadJobsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-jobs',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Payload Job
 */
export const deleteApiPayloadJobsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiPayloadJobsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiPayloadJobsByIdResponses, DeleteApiPayloadJobsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-jobs/{id}',
        ...options
    });
};

/**
 * Find a Payload Job by ID
 */
export const getApiPayloadJobsById = <ThrowOnError extends boolean = false>(options: Options<GetApiPayloadJobsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPayloadJobsByIdResponses, GetApiPayloadJobsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-jobs/{id}',
        ...options
    });
};

/**
 * Update a Payload Job
 */
export const patchApiPayloadJobsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiPayloadJobsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiPayloadJobsByIdResponses, PatchApiPayloadJobsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-jobs/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Payload Locked Documents
 */
export const getApiPayloadLockedDocuments = <ThrowOnError extends boolean = false>(options?: Options<GetApiPayloadLockedDocumentsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPayloadLockedDocumentsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-locked-documents',
        ...options
    });
};

/**
 * Create a new Payload Locked Document
 */
export const postApiPayloadLockedDocuments = <ThrowOnError extends boolean = false>(options?: Options<PostApiPayloadLockedDocumentsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPayloadLockedDocumentsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-locked-documents',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Payload Locked Document
 */
export const deleteApiPayloadLockedDocumentsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiPayloadLockedDocumentsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiPayloadLockedDocumentsByIdResponses, DeleteApiPayloadLockedDocumentsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-locked-documents/{id}',
        ...options
    });
};

/**
 * Find a Payload Locked Document by ID
 */
export const getApiPayloadLockedDocumentsById = <ThrowOnError extends boolean = false>(options: Options<GetApiPayloadLockedDocumentsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPayloadLockedDocumentsByIdResponses, GetApiPayloadLockedDocumentsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-locked-documents/{id}',
        ...options
    });
};

/**
 * Update a Payload Locked Document
 */
export const patchApiPayloadLockedDocumentsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiPayloadLockedDocumentsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiPayloadLockedDocumentsByIdResponses, PatchApiPayloadLockedDocumentsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-locked-documents/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Payload Preferences
 */
export const getApiPayloadPreferences = <ThrowOnError extends boolean = false>(options?: Options<GetApiPayloadPreferencesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPayloadPreferencesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-preferences',
        ...options
    });
};

/**
 * Create a new Payload Preference
 */
export const postApiPayloadPreferences = <ThrowOnError extends boolean = false>(options?: Options<PostApiPayloadPreferencesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPayloadPreferencesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-preferences',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Payload Preference
 */
export const deleteApiPayloadPreferencesById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiPayloadPreferencesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiPayloadPreferencesByIdResponses, DeleteApiPayloadPreferencesByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-preferences/{id}',
        ...options
    });
};

/**
 * Find a Payload Preference by ID
 */
export const getApiPayloadPreferencesById = <ThrowOnError extends boolean = false>(options: Options<GetApiPayloadPreferencesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPayloadPreferencesByIdResponses, GetApiPayloadPreferencesByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-preferences/{id}',
        ...options
    });
};

/**
 * Update a Payload Preference
 */
export const patchApiPayloadPreferencesById = <ThrowOnError extends boolean = false>(options: Options<PatchApiPayloadPreferencesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiPayloadPreferencesByIdResponses, PatchApiPayloadPreferencesByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-preferences/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Payload Migrations
 */
export const getApiPayloadMigrations = <ThrowOnError extends boolean = false>(options?: Options<GetApiPayloadMigrationsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPayloadMigrationsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-migrations',
        ...options
    });
};

/**
 * Create a new Payload Migration
 */
export const postApiPayloadMigrations = <ThrowOnError extends boolean = false>(options?: Options<PostApiPayloadMigrationsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPayloadMigrationsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-migrations',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Payload Migration
 */
export const deleteApiPayloadMigrationsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiPayloadMigrationsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiPayloadMigrationsByIdResponses, DeleteApiPayloadMigrationsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-migrations/{id}',
        ...options
    });
};

/**
 * Find a Payload Migration by ID
 */
export const getApiPayloadMigrationsById = <ThrowOnError extends boolean = false>(options: Options<GetApiPayloadMigrationsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPayloadMigrationsByIdResponses, GetApiPayloadMigrationsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-migrations/{id}',
        ...options
    });
};

/**
 * Update a Payload Migration
 */
export const patchApiPayloadMigrationsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiPayloadMigrationsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiPayloadMigrationsByIdResponses, PatchApiPayloadMigrationsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/payload-migrations/{id}',
        ...options
    });
};

/**
 * Login to User
 */
export const postApiUsersLogin = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersLoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersLoginResponses, PostApiUsersLoginErrors, ThrowOnError>({
        url: '/api/users/login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Logout from User
 */
export const postApiUsersLogout = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersLogoutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/logout',
        ...options
    });
};

/**
 * Refresh User token
 */
export const postApiUsersRefreshToken = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersRefreshTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersRefreshTokenResponses, PostApiUsersRefreshTokenErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/refresh-token',
        ...options
    });
};

/**
 * Get current User
 */
export const getApiUsersMe = <ThrowOnError extends boolean = false>(options?: Options<GetApiUsersMeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiUsersMeResponses, GetApiUsersMeErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/me',
        ...options
    });
};

/**
 * Request User password reset
 */
export const postApiUsersForgotPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersForgotPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersForgotPasswordResponses, PostApiUsersForgotPasswordErrors, ThrowOnError>({
        url: '/api/users/forgot-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Reset User password
 */
export const postApiUsersResetPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersResetPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersResetPasswordResponses, PostApiUsersResetPasswordErrors, ThrowOnError>({
        url: '/api/users/reset-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Verify User
 */
export const postApiUsersVerifyByToken = <ThrowOnError extends boolean = false>(options: Options<PostApiUsersVerifyByTokenData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiUsersVerifyByTokenResponses, PostApiUsersVerifyByTokenErrors, ThrowOnError>({
        url: '/api/users/verify/{token}',
        ...options
    });
};

/**
 * Unlock User
 */
export const postApiUsersUnlock = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersUnlockData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersUnlockResponses, PostApiUsersUnlockErrors, ThrowOnError>({
        url: '/api/users/unlock',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Get the Header
 */
export const getApiGlobalsHeader = <ThrowOnError extends boolean = false>(options?: Options<GetApiGlobalsHeaderData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiGlobalsHeaderResponses, unknown, ThrowOnError>({
        url: '/api/globals/header',
        ...options
    });
};

/**
 * Update the Header
 */
export const postApiGlobalsHeader = <ThrowOnError extends boolean = false>(options?: Options<PostApiGlobalsHeaderData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiGlobalsHeaderResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/globals/header',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Get the Footer
 */
export const getApiGlobalsFooter = <ThrowOnError extends boolean = false>(options?: Options<GetApiGlobalsFooterData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiGlobalsFooterResponses, unknown, ThrowOnError>({
        url: '/api/globals/footer',
        ...options
    });
};

/**
 * Update the Footer
 */
export const postApiGlobalsFooter = <ThrowOnError extends boolean = false>(options?: Options<PostApiGlobalsFooterData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiGlobalsFooterResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/globals/footer',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};