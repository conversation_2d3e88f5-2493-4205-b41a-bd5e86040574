'use client'

import { createContext, useEffect, useMemo, useState } from 'react'
import { getApiUsersMeOptions } from '@/lib/api'
import type { User } from '@/payload-types'
import { useQuery } from '@tanstack/react-query'

export const AuthContext = createContext<{
  currentUser: GetApiUsersMeResponse | null
  setCurrentUser: (user: GetApiUsersMeResponse | null) => void
}>({
  currentUser: null,
  setCurrentUser: () => {}
})

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [currentUser, setCurrentUser] = useState<GetApiUsersMeResponse | null>(null)

  const getCurrentUserQuery = useQuery({
    ...getApiUsersMeOptions(),
    staleTime: 0
  })

  useEffect(() => {
    if (getCurrentUserQuery.data) {
      // If data has a user property, use it; otherwise use data directly
      const responseData = getCurrentUserQuery.data as GetApiUsersMeResponse & {
        user?: GetApiUsersMeResponse
      }
      const user = responseData.user || responseData
      setCurrentUser(user)
    }
  }, [getCurrentUserQuery.data])

  const value = useMemo(
    () => ({ currentUser, setCurrentUser }),
    [currentUser, setCurrentUser]
  )

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
