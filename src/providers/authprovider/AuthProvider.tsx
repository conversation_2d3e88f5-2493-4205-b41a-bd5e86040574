'use client'

import { createContext, useEffect, useMemo, useState } from 'react'
import { getApiUsersMeOptions, postApiUsersLoginOptions } from '@/lib/api'
import type { User } from '@/lib/api-sdk/types.gen'
import { useMutation, useQuery } from '@tanstack/react-query'

export const AuthContext = createContext<{
  currentUser: User | null
  setCurrentUser: (user: User | null) => void
}>({
  currentUser: null,
  setCurrentUser: () => {}
})

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null)

  const getCurrentUserQuery = useQuery({
    ...getApiUsersMeOptions(),
    staleTime: 0
  })

  const authMutation = useMutation({
    ...postApiUsersLoginOptions()
  })

  useEffect(() => {
    if (getCurrentUserQuery.data?.user) {
      setCurrentUser(getCurrentUserQuery.data.user)
    }
  }, [getCurrentUserQuery.data])

  const value = useMemo(
    () => ({
      currentUser,
      setCurrentUser,

      authenticate: authMutation.mutate,
      isAuthenticating: authMutation.isPending,
      authenticateError: authMutation.error
    }),
    [currentUser, setCurrentUser, authMutation]
  )

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
