'use client'

import { createContext, useEffect, useMemo, useState } from 'react'
import { getApiUsersMeOptions } from '@/lib/api'
import type { User } from '@/lib/api-sdk/types.gen'
import { useQuery } from '@tanstack/react-query'

export const AuthContext = createContext<{
  currentUser: User | null
  setCurrentUser: (user: User | null) => void
}>({
  currentUser: null,
  setCurrentUser: () => {}
})

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null)

  const getCurrentUserQuery = useQuery({
    ...getApiUsersMeOptions(),
    staleTime: 0
  })

  useEffect(() => {
    if (getCurrentUserQuery.data) {
      // getCurrentUserQuery.data is already the User object
      setCurrentUser(getCurrentUserQuery.data)
    }
  }, [getCurrentUserQuery.data])

  const value = useMemo(
    () => ({ currentUser, setCurrentUser }),
    [currentUser, setCurrentUser]
  )

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
