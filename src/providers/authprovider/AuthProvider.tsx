'use client'

import { createContext, useEffect, useMemo, useState } from 'react'
import { getApiUsersMeOptions } from '@/lib/api'
import type { GetApiUsersMeResponse } from '@/lib/api-sdk/types.gen'
import { useQuery } from '@tanstack/react-query'

export const AuthContext = createContext<{
  currentUser: GetApiUsersMeResponse | null
  setCurrentUser: (user: GetApiUsersMeResponse | null) => void
}>({
  currentUser: null,
  setCurrentUser: () => {}
})

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [currentUser, setCurrentUser] = useState<GetApiUsersMeResponse | null>(null)

  const getCurrentUserQuery = useQuery({
    ...getApiUsersMeOptions(),
    staleTime: 0
  })

  useEffect(() => {
    if (getCurrentUserQuery.data) {
      setCurrentUser(getCurrentUserQuery.data)
    }
  }, [getCurrentUserQuery.data])

  console.log(currentUser)

  const value = useMemo(
    () => ({ currentUser, setCurrentUser }),
    [currentUser, setCurrentUser]
  )

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
