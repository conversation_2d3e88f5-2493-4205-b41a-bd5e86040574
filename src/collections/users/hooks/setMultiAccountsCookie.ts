import type { CollectionAfterLoginHook } from 'payload';
import {generatePayloadCookie} from 'payload'
import { users } from '@/collections'


export const setMultiAccountsCookie: CollectionAfterLoginHook = async ({
  user,
  token
}) => {
    const userAccountCookie = generatePayloadCookie({
        cookiePrefix: `user-${user.id}`
        token: token,
        collectionAuthConfig: users.auth
      })
  console.log(token)
  console.log(user)
  return user
}