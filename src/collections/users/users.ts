import type { CollectionConfig } from 'payload'
import { anyone, isSelfOrSuperAdmin, isSuperAdminAccess } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import { tenantsArrayField } from '@payloadcms/plugin-multi-tenant/fields'

const defaultTenantArrayField = tenantsArrayField({
  tenantsArrayFieldName: 'teams',
  tenantsArrayTenantFieldName: 'team',
  tenantsCollectionSlug: 'teams',
  arrayFieldAccess: {},
  tenantFieldAccess: {},
  rowFields: [
    {
      name: 'roles',
      type: 'select',
      defaultValue: ['teamviewer'],
      hasMany: true,
      options: ['teamadmin', 'teamviewer'],
      required: true
    }
  ]
})

const {
  users: { slug: usersSlug }
} = collections

export const users: CollectionConfig = {
  slug: usersSlug,
  access: {
    admin: isSuperAdminAccess,
    create: anyone,
    delete: isSuperAdminAccess,
    read: isSelfOrSuperAdmin,
    update: isSelfOrSuperAdmin
  },
  admin: {
    defaultColumns: ['firstName', 'email'],
    useAsTitle: 'firstName',
    group: AdminCollectionsGroup.application
  },
  auth: true,
  fields: [
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true
    },
    {
      name: 'firstName',
      type: 'text',
      required: true
    },
    {
      name: 'lastName',
      type: 'text',
      required: false
    },
    {
      name: 'profileImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Upload a profile image for the user',
        position: 'sidebar'
      },
      required: false
    },
    {
      type: 'array',
      name: 'socialAccounts',
      admin: {
        description: 'Social accounts for the user',
        position: 'sidebar'
      },
      fields: [
        {
          name: 'provider',
          type: 'text',
          required: true,
          unique: true
        },
        {
          name: 'providerId',
          type: 'text',
          required: true,
          unique: true
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true
        },
        {
          name: 'linkedAt',
          type: 'date',
          defaultValue: new Date()
        }
      ]
    },
    {
      name: 'roles',
      type: 'select',
      options: [
        { label: 'User', value: collections.users.roles.user },
        { label: 'Editor', value: collections.users.roles.editor },
        { label: 'Super Admin', value: collections.users.roles.superAdmin }
      ],
      hasMany: true,
      required: true,
      defaultValue: [collections.users.roles.user]
    },
    {
      ...defaultTenantArrayField,
      saveToJWT: false,
      admin: {
        ...(defaultTenantArrayField?.admin || {}),
        position: 'sidebar'
      }
    }
  ],
  timestamps: true
}
