[2025-06-17T18:48:59.020Z] Error: missing input - which OpenAPI specification should we use to generate your output?
Stack:
Error: missing input - which OpenAPI specification should we use to generate your output?
    at Pm (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+openapi-ts@0.73.0_typescript@5.8.3/node_modules/@hey-api/openapi-ts/dist/index.cjs:42:76248)
    at async d$ (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+openapi-ts@0.73.0_typescript@5.8.3/node_modules/@hey-api/openapi-ts/dist/index.cjs:1338:4485)
    at async start (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+openapi-ts@0.73.0_typescript@5.8.3/node_modules/@hey-api/openapi-ts/bin/index.cjs:131:21)
